[WinMerge]
/Executable=C:\Users\<USER>\Desktop\PRJ\GIT_WORKFLOW\__SHELL__\PORTAL\APPS\app_winmerge\exe\WinMergeU.exe
/ContextMenuEnabled=7
/UserTasksFlags=4097
/FirstSelection=
/SecondSelection=
Files\Ext/Item_0=*.*
Files\Ext/Count=1
Files\Left/Item_0=C:\Users\<USER>\Desktop\PRJ\GIT_WORKFLOW\__SHELL__\__UNREGISTER.BAT
Files\Left/Count=4
Files\Left/Item_1=C:\Users\<USER>\Desktop\PRJ\GIT_WORKFLOW\__SHELL__\.gitignore
Files\Left/Item_2=C:\Users\<USER>\Desktop\winm\reg1.reg
Files\Left/Item_3=C:\Users\<USER>\Desktop\winm\reg3.reg
Files\Option/Count=1
Files\Option/Empty=1
Files\Option/Item_0=C:\Users\<USER>\Desktop\PRJ\GIT_WORKFLOW\__SHELL__\shell.nss
Files\Prediffer/Count=0
Files\Prediffer/Empty=1
Files\Right/Count=2
Files\Right/Empty=1
Files\Right/Item_0=C:\Users\<USER>\Desktop\PRJ\GIT_WORKFLOW\__SHELL__\.gitignore
Files\Right/Item_1=C:\Users\<USER>\Desktop\PRJ\GIT_WORKFLOW\__SHELL__\__SHELL__.sublime-project
Files\Unpacker/Count=0
Files\Unpacker/Empty=1
ResizeableDialogs/OpenView=249,327,1829,1065
Settings-Bar0/BarID=32820
Settings-Bar0/XPos=0
Settings-Bar0/YPos=0
Settings-Bar0/Docking=1
Settings-Bar0/MRUDockID=0
Settings-Bar0/MRUDockLeftPos=0
Settings-Bar0/MRUDockTopPos=0
Settings-Bar0/MRUDockRightPos=120
Settings-Bar0/MRUDockBottomPos=749
Settings-Bar0/MRUFloatStyle=4
Settings-Bar0/MRUFloatXPos=-2147483648
Settings-Bar0/MRUFloatYPos=0
Settings-Bar1/BarID=32819
Settings-Bar1/XPos=0
Settings-Bar1/YPos=0
Settings-Bar1/Docking=1
Settings-Bar1/MRUDockID=0
Settings-Bar1/MRUDockLeftPos=0
Settings-Bar1/MRUDockTopPos=0
Settings-Bar1/MRUDockRightPos=1912
Settings-Bar1/MRUDockBottomPos=200
Settings-Bar1/MRUFloatStyle=8196
Settings-Bar1/MRUFloatXPos=-2147483648
Settings-Bar1/MRUFloatYPos=0
Settings-Bar2/BarID=59422
Settings-Bar2/Bars=3
Settings-Bar2/Bar#0=0
Settings-Bar2/Bar#1=32819
Settings-Bar2/Bar#2=0
Settings-Bar3/BarID=59420
Settings-Bar3/Bars=3
Settings-Bar3/Bar#0=0
Settings-Bar3/Bar#1=32820
Settings-Bar3/Bar#2=0
Settings-Bar4/BarID=59421
Settings-Bar5/BarID=59420
Settings-SCBar-32819/sizeHorzCX=1912
Settings-SCBar-32819/sizeHorzCY=200
Settings-SCBar-32819/sizeVertCX=120
Settings-SCBar-32819/sizeVertCY=200
Settings-SCBar-32819/sizeFloatCX=120
Settings-SCBar-32819/sizeFloatCY=200
Settings-SCBar-32820/sizeHorzCX=120
Settings-SCBar-32820/sizeHorzCY=200
Settings-SCBar-32820/sizeVertCX=120
Settings-SCBar-32820/sizeVertCY=749
Settings-SCBar-32820/sizeFloatCX=120
Settings-SCBar-32820/sizeFloatCY=200
Settings-Summary/Bars=6
Settings-Summary/ScreenCX=2560
Settings-Summary/ScreenCY=1600
Backup/EnableFile=1
Backup/EnableFolder=0
Backup/GlobalFolder=
Backup/Location=0
Backup/NameAddBak=1
Backup/NameAddTime=0
Custom Colors/0=0xffffff
Custom Colors/1=0xffffff
Custom Colors/10=0xffffff
Custom Colors/11=0xffffff
Custom Colors/12=0xffffff
Custom Colors/13=0xffffff
Custom Colors/14=0xffffff
Custom Colors/15=0xffffff
Custom Colors/2=0xffffff
Custom Colors/3=0xffffff
Custom Colors/4=0xffffff
Custom Colors/5=0xffffff
Custom Colors/6=0xffffff
Custom Colors/7=0xffffff
Custom Colors/8=0xffffff
Custom Colors/9=0xffffff
DefaultSyntaxColors/Bold00=0
DefaultSyntaxColors/Bold01=0
DefaultSyntaxColors/Bold02=0
DefaultSyntaxColors/Bold03=0
DefaultSyntaxColors/Bold04=0
DefaultSyntaxColors/Bold05=0
DefaultSyntaxColors/Bold06=0
DefaultSyntaxColors/Bold07=1
DefaultSyntaxColors/Bold08=0
DefaultSyntaxColors/Bold09=0
DefaultSyntaxColors/Bold10=0
DefaultSyntaxColors/Bold11=0
DefaultSyntaxColors/Bold12=0
DefaultSyntaxColors/Bold13=0
DefaultSyntaxColors/Bold14=0
DefaultSyntaxColors/Bold15=0
DefaultSyntaxColors/Bold16=0
DefaultSyntaxColors/Bold17=0
DefaultSyntaxColors/Bold18=0
DefaultSyntaxColors/Bold19=0
DefaultSyntaxColors/Bold20=0
DefaultSyntaxColors/Bold21=0
DefaultSyntaxColors/Bold22=0
DefaultSyntaxColors/Bold23=0
DefaultSyntaxColors/Bold24=0
DefaultSyntaxColors/Bold25=0
DefaultSyntaxColors/Color00=0x000080
DefaultSyntaxColors/Color01=0xffffff
DefaultSyntaxColors/Color02=0xffffff
DefaultSyntaxColors/Color03=0x000000
DefaultSyntaxColors/Color04=0xf0f0f0
DefaultSyntaxColors/Color05=0xd77800
DefaultSyntaxColors/Color06=0xffffff
DefaultSyntaxColors/Color07=0xff0000
DefaultSyntaxColors/Color08=0x800080
DefaultSyntaxColors/Color09=0x008000
DefaultSyntaxColors/Color10=0x0000ff
DefaultSyntaxColors/Color11=0x606060
DefaultSyntaxColors/Color12=0x000080
DefaultSyntaxColors/Color13=0xc08000
DefaultSyntaxColors/Color14=0xa0a0ff
DefaultSyntaxColors/Color15=0x000000
DefaultSyntaxColors/Color16=0x00ffff
DefaultSyntaxColors/Color17=0x000000
DefaultSyntaxColors/Color18=0x000080
DefaultSyntaxColors/Color19=0x000080
DefaultSyntaxColors/Color20=0x800000
DefaultSyntaxColors/Color21=0xc08000
DefaultSyntaxColors/Color22=0x7fffff
DefaultSyntaxColors/Color23=0x7fff7f
DefaultSyntaxColors/Color24=0x7f7fff
DefaultSyntaxColors/Color25=0xff7f7f
DefaultSyntaxColors/Values=0x00001a
DirView/ColumnOrders=
DirView/ColumnWidths=
DirView3/ColumnOrders=
DirView3/ColumnWidths=
Editor/Marker=
FileTypes/ABAP.exts=abap
FileTypes/ASP.exts=asp;ascx
FileTypes/AutoIt.exts=au3
FileTypes/AutoLISP.exts=lsp;dsl
FileTypes/Basic.exts=bas;vb;vbs;frm;dsm;cls;ctl;pag;dsr
FileTypes/Batch.exts=bat;btm;cmd
FileTypes/C#.exts=cs
FileTypes/C.exts=c;cc;cpp;cxx;h;hpp;hxx;hm;inl;rh;tlh;tli;xs
FileTypes/CSS.exts=css
FileTypes/D.exts=d;di
FileTypes/DCL.exts=dcl;dcc
FileTypes/Fortran.exts=f;f90;f9p;fpp;for;f77
FileTypes/Go.exts=go
FileTypes/HTML.exts=html;htm;shtml;ihtml;ssi;stm;stml;jsp
FileTypes/INI.exts=ini;reg;vbp;isl
FileTypes/InnoSetup.exts=iss
FileTypes/InstallShield.exts=rul
FileTypes/JSON.exts=json
FileTypes/Java.exts=java;jav
FileTypes/JavaScript.exts=js
FileTypes/Lua.exts=lua
FileTypes/MATLAB.exts=m
FileTypes/NSIS.exts=nsi;nsh
FileTypes/PHP.exts=php;php3;php4;php5;phtml
FileTypes/PO.exts=po;pot
FileTypes/Pascal.exts=pas
FileTypes/Perl.exts=pl;pm;plx
FileTypes/PowerShell.exts=ps1
FileTypes/Python.exts=py
FileTypes/REXX.exts=rex;rexx
FileTypes/Resources.exts=rc;dlg;r16;r32;rc2
FileTypes/Ruby.exts=rb;rbw;rake;gemspec
FileTypes/Rust.exts=rs
FileTypes/SIOD.exts=scm
FileTypes/SQL.exts=sql
FileTypes/Sgml.exts=sgml
FileTypes/Shell.exts=sh;conf
FileTypes/Smarty.exts=tpl
FileTypes/TCL.exts=tcl
FileTypes/TEX.exts=tex;sty;clo;ltx;fd;dtx
FileTypes/VHDL.exts=vhd;vhdl;vho
FileTypes/Verilog.exts=v;vh
FileTypes/XML.exts=xml
Font/CharSet=0
Font/ClipPrecision=2
Font/Escapement=0
Font/FaceName=Courier New
Font/Height=-24
Font/Italic=0
Font/Orientation=0
Font/OutPrecision=1
Font/PitchAndFamily=49
Font/PointSize=12
Font/Quality=1
Font/Specified=0
Font/StrikeOut=0
Font/Underline=0
Font/Weight=400
FontDirCompare/CharSet=1
FontDirCompare/ClipPrecision=0
FontDirCompare/Escapement=0
FontDirCompare/FaceName=Segoe UI
FontDirCompare/Height=-18
FontDirCompare/Italic=0
FontDirCompare/Orientation=0
FontDirCompare/OutPrecision=0
FontDirCompare/PitchAndFamily=0
FontDirCompare/PointSize=9
FontDirCompare/Quality=0
FontDirCompare/Specified=0
FontDirCompare/StrikeOut=0
FontDirCompare/Underline=0
FontDirCompare/Weight=400
LineFilters/Values=0
Locale/LanguageId=1033
Merge7z/Enable=1
Merge7z/FilterIndex=1
Merge7z/ProbeSignature=0
PatchCreator/ContextLines=0
PatchCreator/CopyToClipboard=0
PatchCreator/IncludeCmdLine=0
PatchCreator/OpenToEditor=0
PatchCreator/PatchStyle=0
Plugins/OpenInSameFrameType=0
Plugins/UnpackDontCheckExtension=1
Project/Load.CompareOptions=1
Project/Load.FileFilter=1
Project/Load.HiddenItems=1
Project/Load.IncludeSubfolders=1
Project/Load.UnpackerPlugin=1
Project/Open.CompareOptions=1
Project/Open.FileFilter=1
Project/Open.HiddenItems=1
Project/Open.IncludeSubfolders=1
Project/Open.UnpackerPlugin=1
Project/Save.CompareOptions=1
Project/Save.FileFilter=1
Project/Save.HiddenItems=1
Project/Save.IncludeSubfolders=1
Project/Save.UnpackerPlugin=1
Recent File List/File1=
Recent File List/File2=
Recent File List/File3=
Recent File List/File4=
Recent File List/File5=
Recent File List/File6=
Recent File List/File7=
Recent File List/File8=
Recent File List/File9=
ReportFiles/CopyToClipboard=0
ReportFiles/IncludeFileCmpReport=0
ReportFiles/ReportType=0
ResizeableDialogs/OptionsDlg=567,253,1511,1034
Settings/ActiveFrameMax=1
Settings/ActivePane=0
Settings/AdditionalProperties=
Settings/AllowMixedEOL=0
Settings/AskClosingMultipleWindows=0
Settings/AutoCompleteSource=1
Settings/AutoReloadModifiedFiles=1
Settings/AutoResizePanes=0
Settings/AutomaticRescan=0
Settings/BinaryFilePatterns=*.bin;*.frx
Settings/BinaryMethodLimit=67108864
Settings/BreakOnWords=0
Settings/BreakType=1
Settings/CSVDelimiterCharacter=,
Settings/CSVFilePatterns=*.csv
Settings/CloseWithEsc=1
Settings/CloseWithOK=0
Settings/CodepageDefaultCustomValue=1252
Settings/CodepageDefaultMode=0
Settings/CodepageDetection=-1018101759
Settings/ColorScheme=Default
Settings/CompMethod2=0
Settings/CompareThreads=-1
Settings/CompletelyBlankOutIgnoredChanges=0
Settings/CopyGranularity=3
Settings/CustomTempPath=
Settings/DSVDelimiterCharacter=;
Settings/DSVFilePatterns=
Settings/DefaultTextColoring=1
Settings/DiffAlgorithm=0
Settings/DiffContextV2=-1
Settings/DifferenceColor=0x05cbef
Settings/DifferenceDeletedColor=0xc0c0c0
Settings/DifferenceTextColor=0x000000
Settings/DirItemDiffColor=0x7ddef0
Settings/DirItemDiffTextColor=0x000000
Settings/DirItemEqualColor=0xffffff
Settings/DirItemEqualTextColor=0x000000
Settings/DirItemFilteredColor=0xd7f5fa
Settings/DirItemFilteredTextColor=0x000000
Settings/DirItemNotExistAllColor=0xdddddd
Settings/DirItemNotExistAllTextColor=0x000000
Settings/DirMarginColor=0xffffff
Settings/DirViewExpandSubdirs=0
Settings/DirViewSortAscending=1
Settings/DirViewSortCol=-1
Settings/DirViewSortCol3=-1
Settings/EnableImageCompareInFolderCompare=0
Settings/ExternalEditor=%windir%\NOTEPAD.EXE
Settings/FileFilterCurrent=*.*
Settings/FilterCommentsLines=0
Settings/Filters/Shared=0
Settings/HiliteBreakSeparators=.,:;?[](){}<=>`'!"#$%&^~\|@+-*/
Settings/HiliteSyntax=1
Settings/HiliteWordDiff=1
Settings/IgnoreBlankLines=0
Settings/IgnoreCase=0
Settings/IgnoreCodepage=0
Settings/IgnoreEol=0
Settings/IgnoreNumbers=0
Settings/IgnoreRegExp=0
Settings/IgnoreReparsePoints=0
Settings/IgnoreSmallFileTime=0
Settings/IgnoreSpace=0
Settings/ImageBackColor=0xffffff
Settings/ImageColorDistanceThreshold=0x000000
Settings/ImageDiffBlockSize=8
Settings/ImageDiffColorAlpha=0x000046
Settings/ImageDraggingMode=1
Settings/ImageFilePatterns=*.bmp;*.cut;*.dds;*.dng;*.exr;*.g3;*.gif;*.heic;*.hdr;*.ico;*.iff;*.lbm;*.j2k;*.j2c;*.jng;*.jp2;*.jpg;*.jif;*.jpeg;*.jpe;*.jxr;*.wdp;*.hdp;*.koa;*.mng;*.pcd;*.pcx;*.pfm;*.pct;*.pict;*.pic;*.png;*.pbm;*.pgm;*.ppm;*.psd;*.ras;*.sgi;*.rgb;*.rgba;*.bw;*.tga;*.targa;*.tif;*.tiff;*.wap;*.wbmp;*.wbm;*.webp;*.xbm;*.xpm
Settings/ImageInsertionDeletionDetectionMode=0
Settings/ImageOcrResultType=0
Settings/ImageOverlayAlpha=30
Settings/ImageOverlayMode=0
Settings/ImageShowDifferences=1
Settings/ImageUseBackColor=1
Settings/ImageVectorImageZoomRatio=1000
Settings/ImageZoom=1000
Settings/IndentHeuristic=1
Settings/InvertDiffContext=0
Settings/LineNumberUsedAsHeaders=-1
Settings/MRUMax=9
Settings/MainBottom=1202
Settings/MainLeft=79
Settings/MainMax=0
Settings/MainRight=1999
Settings/MainTop=85
Settings/MatchSimilarLines=0
Settings/MergingMode=0
Settings/MovedBlockColor=0x529be4
Settings/MovedBlockDeletedColor=0xc0c0c0
Settings/MovedBlockTextColor=0x000000
Settings/MovedBlocks=0
Settings/OPT_FILE_SIZE_THRESHOLD=67108864
Settings/OptStartPage=22
Settings/PluginsCustomSettingsList=
Settings/PluginsEnabled=1
Settings/PredifferMode=0
Settings/PreserveFiletimes=0
Settings/PreviewPages=0
Settings/ProjectsPath=
Settings/QuickMethodLimit=4194304
Settings/Recurse=1
Settings/RenderingMode=-1
Settings/SNPColor=0xdffafb
Settings/SNPDeletedColor=0xe9e9e9
Settings/SNPTextColor=0x000000
Settings/ScanUnpairedDir=1
Settings/ScrollToFirst=0
Settings/ScrollToFirstInlineDiff=0
Settings/SelectedDifferenceColor=0x7477ef
Settings/SelectedDifferenceDeletedColor=0xc0c0f0
Settings/SelectedDifferenceTextColor=0x000000
Settings/SelectedMovedBlockColor=0x4e70f8
Settings/SelectedMovedBlockDeletedColor=0xa3b5fc
Settings/SelectedMovedBlockTextColor=0x000000
Settings/SelectedSNPColor=0xb4b7ef
Settings/SelectedSNPDeletedColor=0xe0e0f0
Settings/SelectedSNPTextColor=0x000000
Settings/SelectedWordDifferenceColor=0xa0a0ff
Settings/SelectedWordDifferenceDeletedColor=0x6c81c8
Settings/SelectedWordDifferenceTextColor=0x000000
Settings/ShowBinaries=1
Settings/ShowDifferent=1
Settings/ShowDifferentLeftOnly=1
Settings/ShowDifferentMiddleOnly=1
Settings/ShowDifferentRightOnly=1
Settings/ShowFileDialog=0
Settings/ShowIdentical=1
Settings/ShowMissingLeftOnly=1
Settings/ShowMissingMiddleOnly=1
Settings/ShowMissingRightOnly=1
Settings/ShowSkipped=0
Settings/ShowStatusbar=1
Settings/ShowTabbar=1
Settings/ShowToolbar=1
Settings/ShowUniqueLeft=1
Settings/ShowUniqueMiddle=1
Settings/ShowUniqueRight=1
Settings/SingleInstance=0
Settings/SplitHorizontally=0
Settings/StopAfterFirst=0
Settings/SubstitutionFiltersEnabled=0
Settings/TSVFilePatterns=*.tsv
Settings/TabBarAutoMaxWidth=1
Settings/TabSize=4
Settings/TabType=0
Settings/TableAllowNewlinesInQuotes=1
Settings/TableQuoteCharacter="
Settings/ToolbarSize=0
Settings/TreeMode=1
Settings/TrivialDifferenceColor=0xbff2fb
Settings/TrivialDifferenceDeletedColor=0xe9e9e9
Settings/TrivialDifferenceTextColor=0x000000
Settings/UnpackerMode=0
Settings/UseDirCompareColors=1
Settings/UseRecycleBin=1
Settings/UseSystemTempPath=1
Settings/UserFilterPath=C:\Users\<USER>\Documents
Settings/VerifyOpenPaths=1
Settings/ViewEOL=0
Settings/ViewFileMargin=0
Settings/ViewLineNumbers=0
Settings/ViewTopMargin=0
Settings/ViewTopMarginTable=1
Settings/ViewWhitespace=0
Settings/ViewZoom=1000
Settings/WebPageFitToWindow=1
Settings/WebPageShowDifferences=1
Settings/WebPageURLPatternToExclude=
Settings/WebPageURLPatternToInclude=
Settings/WebPageUserDataFolderPerPane=1
Settings/WebPageUserDataFolderType=0
Settings/WebPageViewHeight=600
Settings/WebPageViewWidth=1024
Settings/WebSyncEventFlags=255
Settings/WebSyncEvents=1
Settings/WebUserAgent=
Settings/WebZoom=1000
Settings/WordDifferenceColor=0xade2f1
Settings/WordDifferenceDeletedColor=0x82aaff
Settings/WordDifferenceTextColor=0x000000
Settings/WordWrap=0
Settings/WordWrapTable=0
SubstitutionFilters/Values=0
[WinMerge.TypeInfo]
/Executable=string
/ContextMenuEnabled=int
/UserTasksFlags=int
/FirstSelection=string
/SecondSelection=string
Files\Ext/Item_0=string
Files\Ext/Count=int
Files\Left/Item_0=string
Files\Left/Count=int
Files\Left/Item_1=string
Files\Left/Item_2=string
Files\Left/Item_3=string
Files\Option/Count=int
Files\Option/Empty=int
Files\Option/Item_0=string
Files\Prediffer/Count=int
Files\Prediffer/Empty=int
Files\Right/Count=int
Files\Right/Empty=int
Files\Right/Item_0=string
Files\Right/Item_1=string
Files\Unpacker/Count=int
Files\Unpacker/Empty=int
ResizeableDialogs/OpenView=string
Settings-Bar0/BarID=int
Settings-Bar0/XPos=int
Settings-Bar0/YPos=int
Settings-Bar0/Docking=int
Settings-Bar0/MRUDockID=int
Settings-Bar0/MRUDockLeftPos=int
Settings-Bar0/MRUDockTopPos=int
Settings-Bar0/MRUDockRightPos=int
Settings-Bar0/MRUDockBottomPos=int
Settings-Bar0/MRUFloatStyle=int
Settings-Bar0/MRUFloatXPos=int
Settings-Bar0/MRUFloatYPos=int
Settings-Bar1/BarID=int
Settings-Bar1/XPos=int
Settings-Bar1/YPos=int
Settings-Bar1/Docking=int
Settings-Bar1/MRUDockID=int
Settings-Bar1/MRUDockLeftPos=int
Settings-Bar1/MRUDockTopPos=int
Settings-Bar1/MRUDockRightPos=int
Settings-Bar1/MRUDockBottomPos=int
Settings-Bar1/MRUFloatStyle=int
Settings-Bar1/MRUFloatXPos=int
Settings-Bar1/MRUFloatYPos=int
Settings-Bar2/BarID=int
Settings-Bar2/Bars=int
Settings-Bar2/Bar#0=int
Settings-Bar2/Bar#1=int
Settings-Bar2/Bar#2=int
Settings-Bar3/BarID=int
Settings-Bar3/Bars=int
Settings-Bar3/Bar#0=int
Settings-Bar3/Bar#1=int
Settings-Bar3/Bar#2=int
Settings-Bar4/BarID=int
Settings-Bar5/BarID=int
Settings-SCBar-32819/sizeHorzCX=int
Settings-SCBar-32819/sizeHorzCY=int
Settings-SCBar-32819/sizeVertCX=int
Settings-SCBar-32819/sizeVertCY=int
Settings-SCBar-32819/sizeFloatCX=int
Settings-SCBar-32819/sizeFloatCY=int
Settings-SCBar-32820/sizeHorzCX=int
Settings-SCBar-32820/sizeHorzCY=int
Settings-SCBar-32820/sizeVertCX=int
Settings-SCBar-32820/sizeVertCY=int
Settings-SCBar-32820/sizeFloatCX=int
Settings-SCBar-32820/sizeFloatCY=int
Settings-Summary/Bars=int
Settings-Summary/ScreenCX=int
Settings-Summary/ScreenCY=int
Backup/EnableFile=bool
Backup/EnableFolder=bool
Backup/GlobalFolder=string
Backup/Location=int
Backup/NameAddBak=bool
Backup/NameAddTime=bool
Custom Colors/0=int
Custom Colors/1=int
Custom Colors/10=int
Custom Colors/11=int
Custom Colors/12=int
Custom Colors/13=int
Custom Colors/14=int
Custom Colors/15=int
Custom Colors/2=int
Custom Colors/3=int
Custom Colors/4=int
Custom Colors/5=int
Custom Colors/6=int
Custom Colors/7=int
Custom Colors/8=int
Custom Colors/9=int
DefaultSyntaxColors/Bold00=bool
DefaultSyntaxColors/Bold01=bool
DefaultSyntaxColors/Bold02=bool
DefaultSyntaxColors/Bold03=bool
DefaultSyntaxColors/Bold04=bool
DefaultSyntaxColors/Bold05=bool
DefaultSyntaxColors/Bold06=bool
DefaultSyntaxColors/Bold07=bool
DefaultSyntaxColors/Bold08=bool
DefaultSyntaxColors/Bold09=bool
DefaultSyntaxColors/Bold10=bool
DefaultSyntaxColors/Bold11=bool
DefaultSyntaxColors/Bold12=bool
DefaultSyntaxColors/Bold13=bool
DefaultSyntaxColors/Bold14=bool
DefaultSyntaxColors/Bold15=bool
DefaultSyntaxColors/Bold16=bool
DefaultSyntaxColors/Bold17=bool
DefaultSyntaxColors/Bold18=bool
DefaultSyntaxColors/Bold19=bool
DefaultSyntaxColors/Bold20=bool
DefaultSyntaxColors/Bold21=bool
DefaultSyntaxColors/Bold22=bool
DefaultSyntaxColors/Bold23=bool
DefaultSyntaxColors/Bold24=bool
DefaultSyntaxColors/Bold25=bool
DefaultSyntaxColors/Color00=int
DefaultSyntaxColors/Color01=int
DefaultSyntaxColors/Color02=int
DefaultSyntaxColors/Color03=int
DefaultSyntaxColors/Color04=int
DefaultSyntaxColors/Color05=int
DefaultSyntaxColors/Color06=int
DefaultSyntaxColors/Color07=int
DefaultSyntaxColors/Color08=int
DefaultSyntaxColors/Color09=int
DefaultSyntaxColors/Color10=int
DefaultSyntaxColors/Color11=int
DefaultSyntaxColors/Color12=int
DefaultSyntaxColors/Color13=int
DefaultSyntaxColors/Color14=int
DefaultSyntaxColors/Color15=int
DefaultSyntaxColors/Color16=int
DefaultSyntaxColors/Color17=int
DefaultSyntaxColors/Color18=int
DefaultSyntaxColors/Color19=int
DefaultSyntaxColors/Color20=int
DefaultSyntaxColors/Color21=int
DefaultSyntaxColors/Color22=int
DefaultSyntaxColors/Color23=int
DefaultSyntaxColors/Color24=int
DefaultSyntaxColors/Color25=int
DefaultSyntaxColors/Values=int
DirView/ColumnOrders=string
DirView/ColumnWidths=string
DirView3/ColumnOrders=string
DirView3/ColumnWidths=string
Editor/Marker=string
FileTypes/ABAP.exts=string
FileTypes/ASP.exts=string
FileTypes/AutoIt.exts=string
FileTypes/AutoLISP.exts=string
FileTypes/Basic.exts=string
FileTypes/Batch.exts=string
FileTypes/C#.exts=string
FileTypes/C.exts=string
FileTypes/CSS.exts=string
FileTypes/D.exts=string
FileTypes/DCL.exts=string
FileTypes/Fortran.exts=string
FileTypes/Go.exts=string
FileTypes/HTML.exts=string
FileTypes/INI.exts=string
FileTypes/InnoSetup.exts=string
FileTypes/InstallShield.exts=string
FileTypes/JSON.exts=string
FileTypes/Java.exts=string
FileTypes/JavaScript.exts=string
FileTypes/Lua.exts=string
FileTypes/MATLAB.exts=string
FileTypes/NSIS.exts=string
FileTypes/PHP.exts=string
FileTypes/PO.exts=string
FileTypes/Pascal.exts=string
FileTypes/Perl.exts=string
FileTypes/PowerShell.exts=string
FileTypes/Python.exts=string
FileTypes/REXX.exts=string
FileTypes/Resources.exts=string
FileTypes/Ruby.exts=string
FileTypes/Rust.exts=string
FileTypes/SIOD.exts=string
FileTypes/SQL.exts=string
FileTypes/Sgml.exts=string
FileTypes/Shell.exts=string
FileTypes/Smarty.exts=string
FileTypes/TCL.exts=string
FileTypes/TEX.exts=string
FileTypes/VHDL.exts=string
FileTypes/Verilog.exts=string
FileTypes/XML.exts=string
Font/CharSet=int
Font/ClipPrecision=int
Font/Escapement=int
Font/FaceName=string
Font/Height=int
Font/Italic=bool
Font/Orientation=int
Font/OutPrecision=int
Font/PitchAndFamily=int
Font/PointSize=int
Font/Quality=int
Font/Specified=bool
Font/StrikeOut=bool
Font/Underline=bool
Font/Weight=int
FontDirCompare/CharSet=int
FontDirCompare/ClipPrecision=int
FontDirCompare/Escapement=int
FontDirCompare/FaceName=string
FontDirCompare/Height=int
FontDirCompare/Italic=bool
FontDirCompare/Orientation=int
FontDirCompare/OutPrecision=int
FontDirCompare/PitchAndFamily=int
FontDirCompare/PointSize=int
FontDirCompare/Quality=int
FontDirCompare/Specified=bool
FontDirCompare/StrikeOut=bool
FontDirCompare/Underline=bool
FontDirCompare/Weight=int
LineFilters/Values=int
Locale/LanguageId=int
Merge7z/Enable=bool
Merge7z/FilterIndex=int
Merge7z/ProbeSignature=bool
PatchCreator/ContextLines=int
PatchCreator/CopyToClipboard=bool
PatchCreator/IncludeCmdLine=bool
PatchCreator/OpenToEditor=bool
PatchCreator/PatchStyle=int
Plugins/OpenInSameFrameType=bool
Plugins/UnpackDontCheckExtension=bool
Project/Load.CompareOptions=bool
Project/Load.FileFilter=bool
Project/Load.HiddenItems=bool
Project/Load.IncludeSubfolders=bool
Project/Load.UnpackerPlugin=bool
Project/Open.CompareOptions=bool
Project/Open.FileFilter=bool
Project/Open.HiddenItems=bool
Project/Open.IncludeSubfolders=bool
Project/Open.UnpackerPlugin=bool
Project/Save.CompareOptions=bool
Project/Save.FileFilter=bool
Project/Save.HiddenItems=bool
Project/Save.IncludeSubfolders=bool
Project/Save.UnpackerPlugin=bool
Recent File List/File1=string
Recent File List/File2=string
Recent File List/File3=string
Recent File List/File4=string
Recent File List/File5=string
Recent File List/File6=string
Recent File List/File7=string
Recent File List/File8=string
Recent File List/File9=string
ReportFiles/CopyToClipboard=bool
ReportFiles/IncludeFileCmpReport=bool
ReportFiles/ReportType=int
ResizeableDialogs/OptionsDlg=string
Settings/ActiveFrameMax=bool
Settings/ActivePane=int
Settings/AdditionalProperties=string
Settings/AllowMixedEOL=bool
Settings/AskClosingMultipleWindows=bool
Settings/AutoCompleteSource=int
Settings/AutoReloadModifiedFiles=int
Settings/AutoResizePanes=bool
Settings/AutomaticRescan=bool
Settings/BinaryFilePatterns=string
Settings/BinaryMethodLimit=int
Settings/BreakOnWords=bool
Settings/BreakType=int
Settings/CSVDelimiterCharacter=string
Settings/CSVFilePatterns=string
Settings/CloseWithEsc=int
Settings/CloseWithOK=bool
Settings/CodepageDefaultCustomValue=int
Settings/CodepageDefaultMode=int
Settings/CodepageDetection=int
Settings/ColorScheme=string
Settings/CompMethod2=int
Settings/CompareThreads=int
Settings/CompletelyBlankOutIgnoredChanges=bool
Settings/CopyGranularity=int
Settings/CustomTempPath=string
Settings/DSVDelimiterCharacter=string
Settings/DSVFilePatterns=string
Settings/DefaultTextColoring=bool
Settings/DiffAlgorithm=int
Settings/DiffContextV2=int
Settings/DifferenceColor=int
Settings/DifferenceDeletedColor=int
Settings/DifferenceTextColor=int
Settings/DirItemDiffColor=int
Settings/DirItemDiffTextColor=int
Settings/DirItemEqualColor=int
Settings/DirItemEqualTextColor=int
Settings/DirItemFilteredColor=int
Settings/DirItemFilteredTextColor=int
Settings/DirItemNotExistAllColor=int
Settings/DirItemNotExistAllTextColor=int
Settings/DirMarginColor=int
Settings/DirViewExpandSubdirs=int
Settings/DirViewSortAscending=bool
Settings/DirViewSortCol=int
Settings/DirViewSortCol3=int
Settings/EnableImageCompareInFolderCompare=bool
Settings/ExternalEditor=string
Settings/FileFilterCurrent=string
Settings/FilterCommentsLines=bool
Settings/Filters/Shared=bool
Settings/HiliteBreakSeparators=string
Settings/HiliteSyntax=bool
Settings/HiliteWordDiff=bool
Settings/IgnoreBlankLines=bool
Settings/IgnoreCase=bool
Settings/IgnoreCodepage=bool
Settings/IgnoreEol=bool
Settings/IgnoreNumbers=bool
Settings/IgnoreRegExp=bool
Settings/IgnoreReparsePoints=bool
Settings/IgnoreSmallFileTime=bool
Settings/IgnoreSpace=int
Settings/ImageBackColor=int
Settings/ImageColorDistanceThreshold=int
Settings/ImageDiffBlockSize=int
Settings/ImageDiffColorAlpha=int
Settings/ImageDraggingMode=int
Settings/ImageFilePatterns=string
Settings/ImageInsertionDeletionDetectionMode=int
Settings/ImageOcrResultType=int
Settings/ImageOverlayAlpha=int
Settings/ImageOverlayMode=int
Settings/ImageShowDifferences=bool
Settings/ImageUseBackColor=bool
Settings/ImageVectorImageZoomRatio=int
Settings/ImageZoom=int
Settings/IndentHeuristic=bool
Settings/InvertDiffContext=bool
Settings/LineNumberUsedAsHeaders=int
Settings/MRUMax=int
Settings/MainBottom=int
Settings/MainLeft=int
Settings/MainMax=int
Settings/MainRight=int
Settings/MainTop=int
Settings/MatchSimilarLines=bool
Settings/MergingMode=bool
Settings/MovedBlockColor=int
Settings/MovedBlockDeletedColor=int
Settings/MovedBlockTextColor=int
Settings/MovedBlocks=bool
Settings/OPT_FILE_SIZE_THRESHOLD=int
Settings/OptStartPage=int
Settings/PluginsCustomSettingsList=string
Settings/PluginsEnabled=bool
Settings/PredifferMode=bool
Settings/PreserveFiletimes=bool
Settings/PreviewPages=int
Settings/ProjectsPath=string
Settings/QuickMethodLimit=int
Settings/Recurse=bool
Settings/RenderingMode=int
Settings/SNPColor=int
Settings/SNPDeletedColor=int
Settings/SNPTextColor=int
Settings/ScanUnpairedDir=bool
Settings/ScrollToFirst=bool
Settings/ScrollToFirstInlineDiff=bool
Settings/SelectedDifferenceColor=int
Settings/SelectedDifferenceDeletedColor=int
Settings/SelectedDifferenceTextColor=int
Settings/SelectedMovedBlockColor=int
Settings/SelectedMovedBlockDeletedColor=int
Settings/SelectedMovedBlockTextColor=int
Settings/SelectedSNPColor=int
Settings/SelectedSNPDeletedColor=int
Settings/SelectedSNPTextColor=int
Settings/SelectedWordDifferenceColor=int
Settings/SelectedWordDifferenceDeletedColor=int
Settings/SelectedWordDifferenceTextColor=int
Settings/ShowBinaries=bool
Settings/ShowDifferent=bool
Settings/ShowDifferentLeftOnly=bool
Settings/ShowDifferentMiddleOnly=bool
Settings/ShowDifferentRightOnly=bool
Settings/ShowFileDialog=bool
Settings/ShowIdentical=bool
Settings/ShowMissingLeftOnly=bool
Settings/ShowMissingMiddleOnly=bool
Settings/ShowMissingRightOnly=bool
Settings/ShowSkipped=bool
Settings/ShowStatusbar=bool
Settings/ShowTabbar=bool
Settings/ShowToolbar=bool
Settings/ShowUniqueLeft=bool
Settings/ShowUniqueMiddle=bool
Settings/ShowUniqueRight=bool
Settings/SingleInstance=int
Settings/SplitHorizontally=bool
Settings/StopAfterFirst=bool
Settings/SubstitutionFiltersEnabled=bool
Settings/TSVFilePatterns=string
Settings/TabBarAutoMaxWidth=bool
Settings/TabSize=int
Settings/TabType=int
Settings/TableAllowNewlinesInQuotes=bool
Settings/TableQuoteCharacter=string
Settings/ToolbarSize=int
Settings/TreeMode=bool
Settings/TrivialDifferenceColor=int
Settings/TrivialDifferenceDeletedColor=int
Settings/TrivialDifferenceTextColor=int
Settings/UnpackerMode=bool
Settings/UseDirCompareColors=bool
Settings/UseRecycleBin=bool
Settings/UseSystemTempPath=bool
Settings/UserFilterPath=string
Settings/VerifyOpenPaths=bool
Settings/ViewEOL=bool
Settings/ViewFileMargin=bool
Settings/ViewLineNumbers=bool
Settings/ViewTopMargin=bool
Settings/ViewTopMarginTable=bool
Settings/ViewWhitespace=bool
Settings/ViewZoom=int
Settings/WebPageFitToWindow=bool
Settings/WebPageShowDifferences=bool
Settings/WebPageURLPatternToExclude=string
Settings/WebPageURLPatternToInclude=string
Settings/WebPageUserDataFolderPerPane=bool
Settings/WebPageUserDataFolderType=int
Settings/WebPageViewHeight=int
Settings/WebPageViewWidth=int
Settings/WebSyncEventFlags=int
Settings/WebSyncEvents=bool
Settings/WebUserAgent=string
Settings/WebZoom=int
Settings/WordDifferenceColor=int
Settings/WordDifferenceDeletedColor=int
Settings/WordDifferenceTextColor=int
Settings/WordWrap=bool
Settings/WordWrapTable=bool
SubstitutionFilters/Values=int
