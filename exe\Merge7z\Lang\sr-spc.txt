﻿;!@Lang2@!UTF-8!
;       : <PERSON><PERSON>
;  9.07 : <PERSON><PERSON><PERSON>
;
;
;
;
;
;
;
;
;
0
7-Zip
Serbian - Cyrillic
Српски - ћирилица
401
У реду
Откажи



Да
Не
Затвори
Помоћ

Настави
440
Да за све
Не за све
Стани
Поново
Позадина
На врху
Пауза
Пауза
Да ли сте сигурни да желите да прекинете?
500
Датотека
Уређивање
Преглед
Омиљено
Алати
Помоћ
540
Погледај
Отвори са 7-Zip-ом
Отвори са придруженом програмом
Прегледај
Промени
Преименуј
Копирај у...
Премести у...
Обриши
Подели фајл...
Спој делове...
Својства
Коментар
Израчунајте проверну величину
разлика
Нова фасцикла
Нова датотека
Излаз
600
Изабери све
Поништи избор свега
Обрнути избор
Изабери...
Поништи избор...
Изабери по типу
Поништи избор по типу
700
Иконе
Напоредно слагање
Списак
Детаљи
730
Без сортирања
Раван преглед
2 Прозора
Траке са алаткама
Отвори почетну фасциклу
Горе за један ниво
Хронологија...
Освежавање
750
Рад са архивама
Рад са датотекама
Велика дугмад
Прикажи текст испод дугмади
800
Додај
Изабери
900
Опције...
Benchmark
960
Помоћ...
О програму...
1003
Путања
Назив
Тип
Фасцикла
Величина
Величина у запакованом стању
Атрибути
Креирана
Приступано
Промењено
Солидно
Коментар
Шифровано
Подели пре
Подели после
Речник
ЦРЦ
Тип
Анти
Метод
Оперативни систем
Систем датотека
Корисник
Група
Блок
Коментар
Положај
Префикс путање
Фасцикле
Датотеке
Верзија
Волумен
ВишеВолумена
Офсет
Линкови
Блокови
Волумени

64-бит
Big-endian
Процесор
Физичка величина
Величина заглавља
Провера резултата
Карактеристике
Виртуелна адреса
ИД
Кратко име
Креатор апликације
Величина сектора
Начин
Линк
Грешка
Укупни капацитет
Слободни простор
Величинa cluster-а
Назив
Локално име
Провајдер
2100
Опције
Језик
Језик:
Промене у датотекама
Програм:
Разлика:
2200
Систем
7-Zip отвара следеће типове датотека:
2301
Убаци 7-Zip у системски мени
Каскадни системски мени
Ставке системског менија:
2320
<Folder>
<Archive>
Отвори архиву
Издвој датотеке...
Додај у архиву...
Тестирај архиву
Издвој овде
Издвој у {0}
Додај у {0}
Додај и направи e-mail...
Додај у {0} и направи e-mail
2400
Фасцикле
Радна фасцикла
Користи Windows-ову привремену фасциклу
Тренутна
Наведена:
Користи само за измењиве медије
Наведите локацију за смештање привремених датотека.
2500
Подешавања
Прикажи ".."
Прикажи праве сличице датотека
Прикажи системски мени
Обележи цео ред
Прикажи линије мреже
Један клик за отварање ставке
Алтернативни начин за бирање
Користи велике меморијске блокове
2900
О програму
7-Zip је бесплатан програм.
3000
Систем не може да издвоји потребну количину меморије
Није било никаквих грешака
{0} објекат(а) изабрано
Не могу да креирам Фасциклу '{0}'
Операција освежавања није дозвољена за ову архиву.
Не могу да отворим датотеку '(0)' као архива
Не могу да отворим шифровану архиву '(0)'. Погрешна лозинка?
Неподржан тип архиве
Датотека {0} већ постоји
Датотека '{0}' је измјењена.\nДа ли желите ажурирати архиву?
Није могуће ажурирати датотеку\n'{0}'
Није могуће започети уређивање.
Датотека изгледа као вирус (име датотеке садржи пуно размака у имену).
Операција не може бити позвана из фасцикле која има дугу путању.
Морате да изаберете неку датотеку
Морате да изаберете једну или више датотека
Исувише ставки
3300
Издвајање
Додајем
Тестирање
Отварање у току...
Скенирање...
3400
Издвој
Издвој у:
Наведи локацију где ће се издвајати датотеке из архива.
3410
Путање
Пуна путања
Без путање
3420
Замена
Питај пре него што замениш
Замени без запиткивања
Прескочи постојеће датотеке
Аутоматска промена назива
Аутоматска промена назива постојећих датотека
3500
Потврди замену датотеке
Циљна фасцикла већ садржи датотеку која се тренутно обрађује.
Да ли желите да замените постојећу датотеку
са овом?
{0} бајтова
Аутоматска промена назива
3700
Неподржани метод компресије за '{0}'.
Грешка у '{0}'. Датотека је неисправана.
CRC грешка у '{0}'. Датотека је неисправана.
Грешке у кодирану датотеку '(0)' Погрешна лозинка.
ЦРЦ грешка у шифроване датотеке '(0)' Погрешна лозинка.
3800
Унесите лозинку
Унесите лозинку:
Поново унесите лозинку:
Прикажи лозинку
Лозинке се не поклапају
Користи само енглеска слова, бројева и специјалних знакова (!, #, $, ...) За лозинке
Лозинка је предугачка
Шифра
3900
Протекло време:
Преостало време:
Величина:
Брзина:
Обрађено:
Компресиони однос:
Грешке:
Архиве:
4000
Додај у архиву
Архива:
Надоградња архива:
Формат архиве:
Ниво компресије:
Тип компресије:
Dictionary size:
Word size:
Величина чврстог блока:
Број низа процесора:
Параметри:
Опције
Креирај SFX архиву
Компресија дељене датотека
Шифровање
Метода шифровања:
Шифруј називе датотека
Потребна меморија - компресија:
Потребна меморија - декомпресија:
4050
Без компресије
Брже
Брзо
Нормално
Максимално
Најбрже
4060
Додај и замени датотеке
Освежи и додај датотеке
Озвежи постојеће датотеке
Синхронизуј датотеке
4070
Прегледај
Све датотеке
Не-чврсте
Чврсто
6000
Копирај
Исеци
Копирај у:
Премести у:
Копирање у току...
Премештање у току...
Преименовање у току...
Изаберите одредишну фасциклу.
Операција није подржана.
Грешка при преименовању датотеке или фасцикле
Потврди копирање датотеке
Да ли сте сигурни да желите да копирате датотеке у архиву
6100
Потврдите брисање датотеке
Потврдите брисање фасцикле
Потврдите вишеструко брисање датотека
Јесте ли сигурни да желите да обришете '{0}'?
Јесте ли сигурни да желите да обришете фасциклу '{0}' и сав њен садржај?
Јесте ли сигурни да желите да обришете ове {0} податке?
Брисање у току...
Грешка при брисању датотеке или фасцикле
Систем не може да премести датотеку са дугом путу у корпу за отпатке
6300
Креирај фасциклу
Креирај датотеку
Име фасцикле:
Име датотеке:
Нова фасцикла
Нова датотека
Грешка при креирању фасцикли
Грешка при креирању датотека
6400
Коментар
Коментар:
Одабери
Поништи избор
Маска:
6600
Особине
Хронологија
Дијагностичке поруке
Поруке
7100
Рачунар
Мрежа
Документи
Систем
7200
Додај
Издвој
Тестирај
Копирај
Премести
Избриши
Својства
7300
Подели датотеку
Подели на:
Подели на делове, бајти:
Подела...
Потврда поделе
Да ли сте сигурни да желите да поделите датотеку на (0) дела?
Величине дела мора бити мањи од величине оригиналне датотеке
Неисправна величина волумена
Одређена величина волумена: {0} бајтова.\nДа ли сте сигурни да желите да поделите архиву у тих волумена?
7400
Састави датотеке
Састави у:
Спајање...
Изаберите само први део поделе датотеке
Не могу да откријем датотеку као део поделе
Не може се наћи више од једног дела поделе
7500
Израчунавање проверне величине...
Информација о проверне величине
ЦРЦ порвера за податак:
ЦРЦ провера за податак и имена:
7600
Benchmark
Коришћење меморије:
Компресија
Декомпресија
Оцена
Потпуна оцена
Тренутно
Резултат
Употреба процесора
Рејтинг/Употреба
Пролази:
