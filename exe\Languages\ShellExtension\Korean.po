# This file is part from WinMerge <https://winmerge.org/>
# Released under the "GNU General Public License"
# # Translators:
# * VenusGirl <venusgirl at https://github.com/VenusGirl>
msgid ""
msgstr ""
"Project-Id-Version: WinMerge Shell Extension\n"
"Report-Msgid-Bugs-To: https://bugs.winmerge.org/\n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: Korean <https://github.com/VenusGirl/winmerge>\n"
"Language: ko\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-Basepath: ../../ShellExtension/Languages\n"
"X-Generator: Poedit 3.4.1\n"

#. AFX_TARG_*
#: ShellExtension.rc:19
#, c-format
msgid "AFX_TARG_ENU"
msgstr "AFX_TARG_KOR"

#. LANGUAGE, SUBLANGUAGE
#: ShellExtension.rc:20
#, c-format
msgid "LANG_ENGLISH, SUBLANG_ENGLISH_US"
msgstr "LANG_KOREAN, SUBLANG_DEFAULT"

#: ShellExtension.rc:111
#, c-format
msgid "ShellExtension"
msgstr "쉘확장"

#: ShellExtension.rc:112
#, c-format
msgid "Win&Merge"
msgstr "WinMerge(&M)"

#: ShellExtension.rc:113
#, c-format
msgid "Compare &As"
msgstr "다음으로 비교(&A)"

#: ShellExtension.rc:114
#, c-format
msgid "&Compare"
msgstr "비교(&C)"

#: ShellExtension.rc:115
#, c-format
msgid "Compare&..."
msgstr "비교(&E)..."

#: ShellExtension.rc:116
#, c-format
msgid "Select &Left"
msgstr "왼쪽 선택(&L)"

#: ShellExtension.rc:117
#, c-format
msgid "Select &Middle"
msgstr "가운데 선택(&M)"

#: ShellExtension.rc:118
#, c-format
msgid "Re-select &Left"
msgstr "왼쪽 다시 선택(&L)"
