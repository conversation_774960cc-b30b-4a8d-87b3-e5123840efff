
People who have contributed to WinM<PERSON>ge
---------------------------------------

Original developer, project admin:
* <PERSON> <<EMAIL>>

Project lead:
* <PERSON> <<EMAIL>>

Developers:
* <PERSON> <denis<PERSON><EMAIL>>
* <PERSON> <<EMAIL>>
* <PERSON> <<EMAIL>>
* <PERSON><PERSON> <<EMAIL>>
* <PERSON><PERSON><PERSON> <<EMAIL>>
* <PERSON> (Graphic Design) <<EMAIL>>
* <PERSON><PERSON> <<EMAIL>>

Inactive/past developers:
* <PERSON>
* <PERSON>
* <PERSON>
* <PERSON>
* <PERSON> "<PERSON><PERSON>" <PERSON> (Installer)
* <PERSON><PERSON> <<EMAIL>>

Localization:
* Arabic:
  Downzen team <https://downzen.com>

* Basque:
  <PERSON><PERSON><PERSON> <A<PERSON><EMAIL>>  

* Bulgarian:
  Sld <sld|mail.bg>
  tiger<PERSON> <<EMAIL>>
  <PERSON><PERSON> <yanko<PERSON> at hotmail.com>
  Стоян <stoyan от гмаил>

* Catalan:
  <PERSON><PERSON> <j<PERSON><EMAIL>>
  Domènec <domenec72 at gmail.com>
  Josep Maria Antolín Segura 
  Pere Orga <pere at orga.cat>

* Simplified Chinese:
  Liaobin <liaobin@jite. net>
  xmpdhml <xmpdhml at users.sourceforge.net>
  MaysWind <i at mayswind.net>
  YFdyh000 <yfdyh000 at gmail.com>
  CharlesMengCA <CharlesMeng at outlook.com>
  Yin Gang <zenith.yin at gmail.com>

* Traditional Chinese:
  Koko <<EMAIL>>
  Calvin Lin <ylin77 at gmail.com>
  SiderealArt <admin at siderealart.me>
  abc0922001

* Corsican:
  Patriccollu di Santa Maria è Sichè

* Croatian:
  Hasan Osmanagiæ <<EMAIL>>

* Czech:
  Jiri Tax <<EMAIL>>
  Jan Hryz <<EMAIL>>

* Danish:
  Rolf Egmose
  Christian List <<EMAIL>>

* Dutch:
  Michel Coene
  Thomas De Rocker <<EMAIL>>
  Ronald Schaap <<EMAIL>>

* Finnish:
  Veikko Muurikainen <veikko.muurikainen at kolumbus.fi>

* French:
  Jean-F Jolin <<EMAIL>>
  Gil Andre <<EMAIL>>
  Laurent Ganier <<EMAIL>>
  Dominique Saussereau <<EMAIL>>
  Matthieu Pupat
  gandf
  Lolo S. <slolo2000 at hotmail.com>
  Need74

* Galician:
  Medulio
  Luis A. Martínez Sobrino <luis.martinez.sobrino at gmail.com>
  P0rsche-911

* German:
  Tim Gerundt <<EMAIL>>
  Winfried Peter <<EMAIL>>
  Joerg Schneider <<EMAIL>>
  Mr-Update

* Greek:
  Polyvios J. Simopoulos <<EMAIL>>

* Hungarian:
  Márton Balázs <<EMAIL>>
  Mihalicza József <<EMAIL>>
  Egyed Ferenc <efi99 at freemail.hu>
  boglarkla

* Italian:
  Andrea Decorte <<EMAIL>>
  Antonio Angelo <<EMAIL>>
  Michele Merega <<EMAIL>>
  Michele Locati <<EMAIL>>
  bovirus
  Massimiliano Caniparoli
  Simone Saviolo

* Japanese:
  Takashi Sawanaka <<EMAIL>>
  Rukoto Luther <rukotolucies at hotmail.com>

* Korean:
  Sukjoon <<EMAIL>>
  Lee Jae-Hong <<EMAIL>>
  Sushizang <<EMAIL>>
  sheppaul <sheppaul at gmail.com>
  teamzamong <teamzamong at gmail.com>
  BrainS <29201475+BraINstinct0 at users.noreply.github.com>
  cynilyn <cynilyn at gmail.com>
  gro00 <gheong.ro at gmail.com>
  VenusGirl <https://github.com/VenusGirl>

* Lithuanian:
  Dalius Guzauskas (aka Tichij) <tichij AT mail DOT com>

* Norwegian:
  Hans Fredrik Nordhaug <<EMAIL>>
  FTno

* Persian:
  Abolfazl Rostamzadeh <a.rostamzadeh at gmail.com>

* Polish:
  M.T <<EMAIL>>
  Skiff <<EMAIL>>
  Pawel Wawrzysko <<EMAIL>>
  Mirosław Żylewicz
  Michał Lipok

* Portuguese (Brazilian):
  Wender Firmino <<EMAIL>>
  Felipe Periard Lopes <<EMAIL>>
  Igor Rückert
  jota11
  Gabriel Camargo Fukushima
  Marcello-mco

* Portuguese (Portugal):
  Nelson Simão
  Lippe35
  Hugo Carvalho <<EMAIL>>

* Romanian:
  Cristian Arghiroiu <<EMAIL>>
  Daniel Dumitriu

* Russian:
  Dmitriy S. Aleshkowskiy <<EMAIL>>
  Timon34
  wvxwxvw
  Den1704

* Serbian:
  Ozzii <<EMAIL>>

* Sinhala:
  T G E Perera

* Slovak:
  Majvan <<EMAIL>>
  Ivan Masár <<EMAIL>>
  Jozef Matta <<EMAIL>>

* Slovenian:
  Iztok Osredkar <<EMAIL>>
  Filip Komar <<EMAIL>>
  Jadran Rudec <<EMAIL>>

* Spanish:
  Dean Grimm <<EMAIL>>
  Jesús M. Delgado 'MacK' <<EMAIL>>
  Mario Angel <<EMAIL>>
  Nelson Ariza <<EMAIL>>
  borjafg
  Mauricio Gracia Gutierrez
  P0rsche-911

* Swedish:
  Hans Eriksson <<EMAIL>>
  Göran Håkansson <<EMAIL>>
  pgert <pgert at yahoo.se>
  Timmy Almroth <timmy.almroth at tim-international.net>

* Tamil:
  தமிழ்நேரம் <https://TamilNeram.github.io>

* Turkish:
  Afyonlu <<EMAIL>>
  Ozkan UNVER <<EMAIL>>
  Kaya Zeren <kaya.zeren at zeron.net>

* Ukranian
  Vitaliy Stopchans'kyy <<EMAIL>>
  Warrior <<EMAIL>>
  Vlad
  Serhii Romashko

Other Contributors (code, ideas, testing..):
* James Abbatiello <<EMAIL>>
* Andre Arpin
* Steve Beaudoin
* bulklodd <<EMAIL>>
* Adam Carrivick
* Hern Chen
* Jeremy Dewey
* Jim Fougeron
* Marvin Gouw
* Robert Grange
* black hero <<EMAIL>>
* Jan Hryz
* jwdevel
* Ed_K
* Alberto Kalb
* Joe Kidd <<EMAIL>>
* Robert Knienider
* Ferenc Kubatovics
* René Leonhardt
* Vladimir Lukianov
* LynX <<EMAIL>>
* David Maisonave
* Dmitry Maslov
* Denis Matiukha
* Matthias Mayer
* Harry Mckame
* Oliver Mellet
* Andreas Morsing
* Ryan Mott
* Tim Musschoot
* Marco De Paoli
* Paul <<EMAIL>>
* Chris Paulse <<EMAIL>>
* Dan Pidcock
* Vincent Osele
* Scott Rasmussen
* Michael Richter
* Markus Rollmann
* saitaman <<EMAIL>>
* Philippe Verdy
* Vikrant
* Gilbert Wellisch
* Paul Welter
* Lars Wittenburg
* Etienne Faisant
* Bill Gord
* H.Saido
* Flaviu_
* Jun Tajima
* EugeneLaptev
* ranjwarrior
* elsonwei
* evoc
* devmynote
* Michał Lipok
* Samuel Plentz
* Herman Semenov
* stonee-K
* yuanshouyan1823
* Mauricio Gracia Gutierrez
* HaroldPetersInskipp (Midnight color schema)
* srgank
* baka0815
* Yurii Hordiienko 
* wiera987
* wilfzim
* Mavaddat Javid
* Yutaka-Sawada
* myss
* vlakoff

WinMerge includes code from:
* Jared Breland <<EMAIL>> (Installer's modpath script)
* Brent Corkum (BCMenu)
* Paul DiLascia (CStaticLink) 
* Michael Dunn (CShellFileOp)
* Francis Irving (original conflict file parser)
* Michael P. Mehl (CMessageBoxDialog)
* Cristi Posea (CSizingControlBar)
* Ferdinand Prantl (Crystal Edit syntax rules)
* Keith Rule (CMemDC - memory DC)
* Paul Senzee (String formatting)
* Henry Spencer (CRegExp)
* Andrei Stcherbatchenko (Author of Crystal Edit)
* Sven Wiegand (Crystal Edit)

External components used by WinMerge

* POCO (BSL-1.0) (https://pocoproject.org/)
* boost (BSL-1.0) (https://www.boost.org/)
* frhed (GPL-2.0) (https://frhed.sourceforge.net/)
* WinIMerge (GPL-2.0) (https://github.com/WinMerge/winimerge/)
  * freeimage (GPL-2.0) (https://freeimage.sourceforge.io/)
  * LibXDiff (LGPL-2.1) (https://github.com/git/git/tree/master/xdiff)
* WinWebDiff (BSD-3-Clause) (https://github.com/WinMerge/winwebdiff/)
  * WebView2 (BSD-3-Clause) (https://learn.microsoft.com/ja-jp/microsoft-edge/webview2/)
  * wil (MIT) (https://github.com/microsoft/wil)
  * rapidjson (MIT) (https://rapidjson.org/)
  * LibXDiff (LGPL-2.1) (https://github.com/git/git/tree/master/xdiff)
* Google C++ Testing Framework (BSD-3-Clause) (http://google.github.io/googletest/)
* GNU patch (msys2 package) (GPL-3.0) (https://savannah.gnu.org/projects/patch/, https://github.com/msys2/MSYS2-packages/tree/master/patch)
* 7-zip (LGPL) (https://www.7-zip.org/)
* LibXDiff (LGPL-2.1) (https://github.com/git/git/tree/master/xdiff)
* html-tidy5 (https://github.com/htacg/tidy-html5/blob/next/README/LICENSE.md) (http://www.html-tidy.org/)
* jq (MIT) (https://stedolan.github.io/jq/)
* wil (MIT) (https://github.com/microsoft/wil)
* md4c (MIT) (https://github.com/mity/md4c)
* Inno Setup (https://jrsoftware.org/files/is/license.txt) (https://jrsoftware.org/isinfo.php)

*** NOTE ***
If your name is missing please let us know (or create a pull request).
https://github.com/WinMerge/winmerge

Thanks!
