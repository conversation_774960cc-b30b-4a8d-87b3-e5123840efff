﻿WINMERGE

O WinMerge é uma ferramenta de comparação e união com o código fonte aberto pra windows. O
WinMerge pode comparar ambos: aa pastas e os arquivos apresentando diferenças num formato
visual de texto que é fácil de entender e manipular. O Winmerge pode ser usado como uma
ferramenta de diferenciamento/união ou como um aplicativo solitário.

O WinMerge tem muitas funções de suporte úteis pra fazer comparação, sincronização e
união tão fácil e útil quanto possível. Várias linguagens de programação e outros
formatos de arquivo são de sintaxe destacada.

A versão mais recente do WinMerge e informação sobre o WinMerge estão disponíveis em
https://winmerge.org/.

Início Rápido
===========
Pra aprender como realizar operações básicas após instalar o WinMerge, clique em
Ajuda > Ajuda do WinMerge e navegue até o tópico do início rápido. Ou vá até a
versão da web em https://manual.winmerge.org/Quick_start.html.

Ajuda do WinMerge
============= 
A Ajuda do WinMerge está instalada localmente como um arquivo de ajuda em HTML da Microsoft,
WinMerge.chm, quando você instalar o WinMerge. Pra abrir a Ajuda clique em Ajuda > Ajuda do
WinMerge ou pressione F1 na janela do WinMerge. Na linha de comando execute o executável do
WinMerge com o interruptor /? help.

Você também pode navegar pela versão em HTML da Ajuda da WinMerge em
https://manual.winmerge.org/.

Suporte do WinMerge
================
Perguntas ou sugestões sobre o WinMerge? Um bom lugar pra iniciar é o fórum	da comunidade
do Winmerge em https://forums.winmerge.org/. Os desenvolvedores frequentemente lêem e
respondem a perguntas em ambos os fóruns. Use o fórum da discussão aberta pra
problemas gerais do WinMerge tais como perguntas sobre o uso. Use o fórum dos
desenvolvedores pra problemas do desenvolvimento do WinMerge.

Bugs e pedidos pra novas funções
=========================
Se um problema não é resolvido nos fóruns do WinMerge, verifique os rastreadores do projeto:
vá em https://project.winmerge.org/ e clique num link no menus dos rastreadores tipo o dos
Bugs e Pedidos pra Novas Funções aonde você pode navegar ou submeter itens.

Se você submeter um bug por favor inclua o número da versão do WinMerge no seu relatório.
Você pode gerar um registro da configuração clicando em Ajuda > Configuração. Por favor
anexe o registro da configuração no relatório dos bugs; ele tem muita informação útil
pros desenvolvedores.


- Os Desenvolvedores do WinMerge
