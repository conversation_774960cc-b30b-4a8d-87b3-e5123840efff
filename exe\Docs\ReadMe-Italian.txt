﻿WinMerge

WinMerge è uno strumento open source di comparazione e fusione per Windows. WinMerge
confronta cartelle e file, presentando le differenze in un formato di testo visivo
facile da capire e gestire. WinMerge può essere usato come uno strumento esterno di 
differenziazione/fusione o come applicazione autonoma.

WinMerge ha molte utili funzioni di supporto per confrontare, sincronizzare, e unire
elementi nel modo più semplice e utile possibile. Sono evidenziati dalla sintassi diversi
linguaggi di programmazione e altri formati di file.

La versione più recente di WinMerge e le informazioni su WinMerge sono disponibili 
all'indirizzo web https://winmerge.org/.

Avvio veloce
===========
Per informazioni su come eseguire le operazioni di base dopo l'installazione di WinMerge,
fai clic su 'Aiuto' -> 'Aiuto WinMerge' e vai all'argomento 'Avvio rapido'. 
Oppure, vai alla pagina web https://manual.winmerge.org/Quick_start.html.

Guida di WinMerge
=============
Quando si installa WinMerge la Guida di WinMerge è installata localmente come file della 
Guida HTML Microsoft (WinMerge.chm).
Per aprire la Guida, fai clic su Guida -> Guida di WinMerge o premi F1 nella finestra di 
WinMerge. Da riga di comando, esegui il file  WinMerge con /? per avviare la Guida.

È inoltre possibile sfogliare la versione HTML della Guida di WinMerge all'indirizzo web
https://manual.winmerge.org/.

Supporto WinMerge
================
Hai domande o suggerimenti su WinMerge? Un buon punto di partenza è la bacheca della community
di WinMerge disponibile alla pagina https://forums.winmerge.org/. Gli sviluppatori frequentemente
leggono e rispondono alle domande in entrambi i forum. Usa il forum di discussione aperto per
i problemi generali di WinMerge, come domande sull'uso. Usa il forum degli sviluppatori per i 
problemi sullo sviluppo di WinMerge.

Bug e richieste di funzionalità
=========================
Se hai un problema che non è stato risolto nel forum WinMerge, controlla il tracker. Controlla il
tracker del progetto. Vai su https://project.winmerge.org/ e fai clic su un collegamento nel menu 
tracker, come 'Bug' e 'Feature Requests', in cui è possibile consultare o inviare richieste.

Se segnali un bug, includi nel rapporto il numero della versione di WinMerge. È possibile generare
un registro di configurazione facendo clic su Guida -> Configurazione.
Allega alla segnalazione del bug il registro di configurazione. È molto utile perchè contiene
informazioni utili per gli sviluppatori.

- Gli sviluppatori WinMerge