# This file is part from WinMerge <https://winmerge.org/>
# Released under the "GNU General Public License"
#
# Maintainer:
# * Lolo S. <slolo2000 at hotmail.com>
#
# Translators:
# * Lolo S. <slolo2000 at hotmail.com>
# * Need74 <need74 at free.fr>
#
msgid ""
msgstr ""
"Project-Id-Version: WinMerge\n"
"Report-Msgid-Bugs-To: https://bugs.winmerge.org/\n"
"POT-Creation-Date: 2023-02-19 00:07+0000\n"
"PO-Revision-Date: 2024-03-30 10:14+0100\n"
"Last-Translator: Lolo S. <<EMAIL>>\n"
"Language-Team: French <<EMAIL>>\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-Basepath: ../../ShellExtension/Languages\n"
"X-Generator: Poedit 3.4.2\n"

#. AFX_TARG_*
#: ShellExtension.rc:19
#, c-format
msgid "AFX_TARG_ENU"
msgstr "AFX_TARG_FRA"

#. LANGUAGE, SUBLANGUAGE
#: ShellExtension.rc:20
#, c-format
msgid "LANG_ENGLISH, SUBLANG_ENGLISH_US"
msgstr "LANG_FRENCH, SUBLANG_FRENCH"

#: ShellExtension.rc:111
#, c-format
msgid "ShellExtension"
msgstr "Extension du Shell"

#: ShellExtension.rc:112
#, c-format
msgid "Win&Merge"
msgstr "Win&Merge"

#: ShellExtension.rc:113
#, c-format
msgid "Compare &As"
msgstr "Comparer c&omme =>"

#: ShellExtension.rc:114
#, c-format
msgid "&Compare"
msgstr "&Comparer"

#: ShellExtension.rc:115
#, c-format
msgid "Compare&..."
msgstr "Comparer&..."

#: ShellExtension.rc:116
#, c-format
msgid "Select &Left"
msgstr "Sélectionner &Gauche"

#: ShellExtension.rc:117
#, c-format
msgid "Select &Middle"
msgstr "Sélectionner &Milieu"

#: ShellExtension.rc:118
#, c-format
msgid "Re-select &Left"
msgstr "Re-sélectionner &Gauche"
