=======================================================================
PlantUML : a free UML diagram generator
========================================================================

(C) Copyright 2009-2017, Arnaud Roques

Project Info:  http://plantuml.com

If you like this project or if you find it useful, you can support us at:

http://plantuml.com/patreon (only 1$ per month!)
http://plantuml.com/paypal

PlantUML is free software; you can redistribute it and/or modify it
under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 3 of the License, or
(at your option) any later version.

PlantUML distributed in the hope that it will be useful, but
WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public
License for more details.

You should have received a copy of the GNU General Public
License along with this library; if not, write to the Free Software
Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301,
USA.

PlantUML can occasionally display sponsored or advertising messages. Those
messages are usually generated on welcome or error images and never on
functional diagrams.
 
Images (whatever their format : PNG, SVG, EPS...) generated by running PlantUML
are owned by the author of their corresponding sources code (that is, their
textual description in PlantUML language). Those images are not covered by
the GPL license.

The generated images can then be used without any reference to the GPL license.
It is not even necessary to stipulate that they have been generated with PlantUML,
although this will be appreciated by the PlantUML team.

There is an exception : if the textual description in PlantUML language is also covered
by a license (like the GPL), then the generated images are logically covered
by the very same license.

Icons provided by OpenIconic :  https://useiconic.com/open
Archimate sprites provided by Archi :  http://www.archimatetool.com
ASCIIMathML (c) Peter Jipsen http://www.chapman.edu/~jipsen
ASCIIMathML (c) David Lippman http://www.pierce.ctc.edu/dlippman
