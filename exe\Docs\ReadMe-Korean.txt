﻿WINMERGE

WinMerge는 Windows용 오픈 소스 비교 및 병합 도구입니다. WinMerge는
폴더와 파일을 모두 비교하여 시각적 텍스트 형식의 차이를 쉽게 이해하고
처리할 수 있습니다. WinMerge는 외부 차분/병합 도구 또는 독립 실행형
응용 프로그램으로 사용할 수 있습니다.

WinMerge에는 비교, 동기화 및 병합을 가능한 한 쉽고 유용하게 만드는
많은 지원 기능이 있습니다. 여러 프로그래밍 언어 및 기타 파일 형식이
구문으로 강조 표시됩니다.

최신 WinMerge 버전 및 WinMerge 정보는
https://winmerge.org/에서 확인할 수 있습니다.

빠른 시작
===========
WinMerge를 설치한 후 기본 작업을 수행하는 방법에 대해 알아보려면
도움말>WinMerge 도움말을 클릭하고 빠른 시작 항목으로 이동하십시오.
또는 https://manual.winmerge.org/Quick_start.html의 웹 버전으로 이동하십시오.

WinMerge 도움말
============= 
WinMerge 도움말은 WinMerge를 설치할 때 Microsoft HTML 도움말 파일인
WinMerge.chm으로 로컬로 설치됩니다. 도움말을 열려면 도움말>WinMerge
도움말을 클릭하거나 WinMerge 창에서 F1을 누릅니다. 명령줄에서 /? 도움말
스위치를 사용하여 WinMerge 실행 파일을 실행합니다.

https://manual.winmerge.org/에서 WinMerge 도움말의
HTML 버전을 찾아볼 수도 있습니다.

WinMerge 지원
================
WinMerge에 대한 질문이나 제안이 있습니까? 시작하기 좋은 곳은
https://forums.winmerge.org/의 WinMerge 커뮤니티 게시판입니다.
개발자는 두 포럼의 질문을 자주 읽고 답변합니다. 사용법에 대한
질문과 같은 일반적인 WinMerge 문제에 대해서는 열린 토론 포럼을
사용하십시오. WinMerge 개발 문제에 대한 개발자 포럼을 사용하십시오.

버그 및 기능 요청
=========================
WinMerge 포럼에서 문제가 해결되지 않으면 프로젝트 추적기를 확인하십시오.
https://project.winmerge.org/,으로 이동하여 추적기 메뉴에서 항목을 찾거나
제출할 수 있는 버그 및 기능 요청과 같은 링크를 클릭하십시오.

버그를 제출하는 경우 보고서에 WinMerge 버전 번호를 포함하십시오.
도움말>구성을 클릭하여 구성 로그를 생성할 수 있습니다.
구성 로그를 버그 보고서에 첨부하십시오. 개발자에게 유용한 정보가
많이 있습니다.


- WinMerge 개발자
