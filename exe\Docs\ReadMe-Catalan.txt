﻿WINMERGE

El WinMerge és una utilitat de comparació i combinació de fitxers de codi obert 
que corre a totes les versions modernes del Windows. Algunes característiques 
requereixen d'instal·lacions o components addicionals.

La darrera versió del WinMerge i altra informació del mateix és troba disponible a
https://winmerge.org

Inici ràpid amb el WinMerge:
Llegiu el capítol Quick-start del manual en línia per a començar amb el WinMerge:
https://manual.winmerge.org/Quick_start.html

Manual HTML:
El manual està disponible en línia a
https://manual.winmerge.org/
Possiblement també estigui instal·lada localment (si així es va triar) i es pot 
baixar separadament des de https://winmerge.org/ (veieu la documentació)

Suport de seqüències:
Per tal de treballar amb fitxers de seqüències caldrà tenir el Windows Scripting Host 
instal·lat. Si rebeu qualsevol error relacionat amb els vostres fitxers de seqüències 
visiteu
https://msdn.microsoft.com/library/default.asp?url=/downloads/list/webdev.asp
per tal d'assegurar-vos que el vostre Scripting Host està actualitzat i no corromput.

Suport:
Els desenvolupadors miren de respondre questions als forums del WinMerge a Sourceforge.net:
https://sourceforge.net/forum/?group_id=13216

Errors i peticions de característiques:
Es recomana que envieu els errors i els suggeriments per a noves caracteristiques 
als seguidors (trackers) d'errors i peticions de millores (RFE) a sourceforge.net.

Seguidor d'errors (Bug tracker):
https://sourceforge.net/tracker/?group_id=13216&atid=113216
En reportar un error, si us plau digueu-nos el número de versió del WinMerge!
Les versions del WinMerge 2.2.0 i posteriors poden generar un registre de 
configuració 'Configuration Log' des del menú Ajuda/Configuració. Adjunteu (com a 
fitxer adjunt) a l'informe de l'error, conté molta informació util per als desenvolupadors.

Seguidor de petició de millores (RFE tracker):
https://sourceforge.net/tracker/?group_id=13216&atid=363216


Els desenvolupadors del WinMerge
