﻿# This file is part of Frhed <http://frhed.sourceforge.net/>
# Released under the "GNU General Public License"
#
# Translators:
# * <PERSON> <tim at gerundt.de>
#
# ID line follows -- this is updated by SVN
# $Id: de.po 697 2009-06-10 11:30:30Z kimmov $
#
msgid ""
msgstr ""
"Project-Id-Version: Frhed\n"
"Report-Msgid-Bugs-To: http://sourceforge.net/tracker/?group_id=13216&atid=113216\n"
"POT-Creation-Date: \n"
"PO-Revision-Date: 2020-08-21 00:20+0000\n"
"Last-Translator: Mr. Update\n"
"Language-Team: German <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Poedit-Language: German\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-Basepath: ../../FRHED\n"

#. LANGUAGE, SUBLANGUAGE
#: heksedit.rc:5
#, c-format
msgid "LANG_ENGLISH, SUBLANG_ENGLISH_US"
msgstr "LANG_GERMAN, SUBLANG_GERMAN"

#. Codepage
#: heksedit.rc:6
#, c-format
msgid "1252"
msgstr "65001"

#: heksedit.rc:12
#, c-format
msgid "&File"
msgstr "&Datei"

#: heksedit.rc:14
#, c-format
msgid "&New\tCtrl+N"
msgstr "&Neu\tStrg+N"

#: heksedit.rc:15
#, c-format
msgid "&Open...\tCtrl+O"
msgstr "Ö&ffnen...\tStrg+O"

#: heksedit.rc:16
#, c-format
msgid "Open pa&rtially...\tAlt+P"
msgstr "&Teilweise öffnen..\tAlt+P"

#: heksedit.rc:18
#, c-format
msgid "&Save\tCtrl+S"
msgstr "&Speichern\tStrg+S"

#: heksedit.rc:19
#, c-format
msgid "Save &As...\tCtrl+J"
msgstr "Speichern &unter...\tStrg+J"

#: heksedit.rc:20
#, c-format
msgid "Save selec&tion as...\tCtrl+Shift+O"
msgstr "&Markierung speichern unter...\tStrg+Umschalt+O"

#: heksedit.rc:22
#, c-format
msgid "Re&vert\tCtrl+Shift+R"
msgstr "Rückgängig machen\tStrg+Umschalt+R"

#: heksedit.rc:23
#, c-format
msgid "&Insert file...\tCtrl+Shift+I"
msgstr "Datei einfügen...\tStrg+Umschalt+I"

#: heksedit.rc:24
#, c-format
msgid "&Delete file...\tCtrl+Shift+D"
msgstr "Datei löschen...\tStrg+Umschalt+D"

#: heksedit.rc:25
#, c-format
msgid "Close File\tCtrl+Shift+X"
msgstr "Datei schließen\tStrg+Umschalt+X"

#: heksedit.rc:27
#, c-format
msgid "&Export as hexdump..."
msgstr "Als Hexdump exportieren..."

#: heksedit.rc:28
#, c-format
msgid "I&mport from hexdump...\tCtrl+Shift+H"
msgstr "Aus Hexdump importieren...\tStrg+Umschalt+H"

#: heksedit.rc:40
#, c-format
msgid "E&xit\tAlt+F4"
msgstr "&Beenden\tAlt+F4"

#: heksedit.rc:42
#, c-format
msgid "&Disk"
msgstr "&Laufwerk"

#: heksedit.rc:44
#, c-format
msgid "&Open drive..."
msgstr "Laufwerk ö&ffnen..."

#: heksedit.rc:45
#, c-format
msgid "&Close drive"
msgstr "Laufwerk &schließen"

#: heksedit.rc:47
#: heksedit.rc:190
#, c-format
msgid "Goto &First Sector"
msgstr "Gehe zum &ersten Sektor"

#: heksedit.rc:48
#, c-format
msgid "Goto &Next Sector\tCtrl+Shift+PgDn"
msgstr "Gehe zum &nächsten Sektor\tStrg+Umschalt+Bild runter"

#: heksedit.rc:49
#, c-format
msgid "Goto &Previous Sector\tCtrl+Shift+PgUp"
msgstr "Gehe zum &vorherigen Sektor\tStrg+Umschalt+Bild hoch"

#: heksedit.rc:51
#: heksedit.rc:193
#, c-format
msgid "Goto &Last Sector"
msgstr "Gehe zum &letzten Sektor"

#: heksedit.rc:53
#: heksedit.rc:195
#, c-format
msgid "Goto &Sector #..."
msgstr "Gehe zu &Sektor #..."

#: heksedit.rc:55
#, c-format
msgid "&Edit"
msgstr "&Bearbeiten"

#: heksedit.rc:57
#, c-format
msgid "&Undo\tCtrl+Z"
msgstr "Rückgängig\tStrg+Z"

#: heksedit.rc:58
#, c-format
msgid "&Redo\tCtrl+Y"
msgstr "Wiederholen\tStrg+Y"

#: heksedit.rc:60
#: heksedit.rc:177
#, c-format
msgid "Cu&t...\tCtrl+X"
msgstr "Ausschneiden...\tStrg+X"

#: heksedit.rc:61
#: heksedit.rc:178
#, c-format
msgid "&Copy...\tCtrl+C"
msgstr "Kopieren...\tStrg+C"

#: heksedit.rc:62
#: heksedit.rc:179
#, c-format
msgid "&Paste...\tCtrl+V"
msgstr "Einfügen...\tStrg+V"

#: heksedit.rc:63
#, c-format
msgid "Paste &with dialog...\tCtrl+Shift+V"
msgstr "Einfügen mit Dialog...\tStrg+Umschalt+V"

#: heksedit.rc:64
#, c-format
msgid "&Move/Copy bytes...\tCtrl+Alt+M"
msgstr "Bytes verschieben/kopieren...\tStrg+Alt+M"

#: heksedit.rc:65
#, c-format
msgid "Reverse bytes...\tCtrl+Alt+R"
msgstr "Bytes umkehren...\tStrg+Alt+R"

#: heksedit.rc:66
#, c-format
msgid "&Append...\tCtrl+P"
msgstr "Anhängen...\tStrg+P"

#: heksedit.rc:67
#: heksedit.rc:180
#, c-format
msgid "&Delete\tDel"
msgstr "Löschen\tEntf"

#: heksedit.rc:68
#, c-format
msgid "&Select all\tCtrl+A"
msgstr "Alles auswählen\tStrg+A"

#: heksedit.rc:69
#, c-format
msgid "Select &block...\tCtrl+E"
msgstr "Block auswählen...\tStrg+E"

#: heksedit.rc:70
#, c-format
msgid "&Fill selection...\tCtrl+Shift+F"
msgstr "Auswahl ausfüllen...\tStrg+Umschalt+F"

#: heksedit.rc:72
#, c-format
msgid "Toggle h&ex/character editing\tTAB"
msgstr "Hex-/Zeichenbearbeitung wechseln\tTab"

#: heksedit.rc:73
#, c-format
msgid "Toggle keyboard I&nsert/Overwrite mode\tIns"
msgstr "Einfügen-/Überschreiben-Modus wechseln\tEinfg"

#: heksedit.rc:75
#, c-format
msgid "Read-only mode\tAlt+R"
msgstr "Schreibschutz-Modus\tAlt+R"

#: heksedit.rc:77
#, c-format
msgid "&Find and Replace"
msgstr "Suchen und Ersetzen"

#: heksedit.rc:79
#: heksedit.rc:182
#, c-format
msgid "&Find...\tCtrl+F"
msgstr "Suchen...\tStrg+F"

#: heksedit.rc:80
#, c-format
msgid "&Replace...\tCtrl+H"
msgstr "Ersetzen...\tStrg+H"

#: heksedit.rc:81
#, c-format
msgid "Find &next\tF3"
msgstr "Weitersuchen\tF3"

#: heksedit.rc:82
#, c-format
msgid "Find &previous\tShift+F3"
msgstr "Rückwärts suchen\tUmschalt+F3"

#: heksedit.rc:84
#: heksedit.rc:186
#, c-format
msgid "&Go To...\tCtrl+G"
msgstr "Gehe zu...\tStrg+G"

#: heksedit.rc:86
#, c-format
msgid "Enter decimal &value...\tCtrl+D"
msgstr "Dezimalwert eingeben...\tStrg+D"

#: heksedit.rc:88
#, c-format
msgid "Man&ipulate bits...\tCtrl+B"
msgstr "Bits manipulieren...\tStrg+B"

#: heksedit.rc:89
#, c-format
msgid "C&ompare from current offset...\tCtrl+M"
msgstr "Ab aktuellem Offset vergleichen...\tStrg+M"

#: heksedit.rc:91
#, c-format
msgid "Get f&loating point value...\tCtrl+L"
msgstr "Fließkommawert erhalten...\tStrg+L"

#: heksedit.rc:92
#, c-format
msgid "File properties...\tAlt+Enter"
msgstr "Dateieigenschaften...\tAlt+Enter"

#: heksedit.rc:93
#, c-format
msgid "Appl&y template...\tAlt+T"
msgstr "Vorlage anwenden...\tAlt+T"

#: heksedit.rc:94
#, c-format
msgid "Open in text editor\tF5"
msgstr "Im Texteditor öffnen\tF5"

#: heksedit.rc:96
#, c-format
msgid "&View"
msgstr "&Ansicht"

#: heksedit.rc:98
#, c-format
msgid "Scroll &left\tCtrl+Arrow Left"
msgstr "Nach links scrollen\tStrg+Links"

#: heksedit.rc:99
#, c-format
msgid "Scroll &right\tCtrl+Arrow Right"
msgstr "Nach rechts scrollen\tStrg+Rechts"

#: heksedit.rc:100
#, c-format
msgid "Scroll &up\tCtrl+Arrow Up"
msgstr "Nach oben scrollen\tStrg+Hoch"

#: heksedit.rc:101
#, c-format
msgid "Scroll &down\tCtrl+Arrow Down"
msgstr "Nach unten scrollen\tStrg+Runter"

#: heksedit.rc:102
#, c-format
msgid "Scroll up a page\tCtrl+Page Up"
msgstr "Eine Seite hoch scrollen\tStrg+Bild hoch"

#: heksedit.rc:103
#, c-format
msgid "Scroll down a page\tCtrl+Page Down"
msgstr "Einen Seite runter scrollen\tStrg+Bild runter"

#: heksedit.rc:105
#, c-format
msgid "Zoom in\tCtrl++"
msgstr "Vergrößern\tStrg++"

#: heksedit.rc:106
#, c-format
msgid "Zoom out\tCtrl+-"
msgstr "Verkleinern\tStrg+-"

#: heksedit.rc:107
#, c-format
msgid "Zoom normal\tCtrl+*"
msgstr "Normal\tStrg+*"

#: heksedit.rc:109
#, c-format
msgid "&Options"
msgstr "&Einstellungen"

#: heksedit.rc:111
#, c-format
msgid "&View settings...\tCtrl+I"
msgstr "Einstellungen...\tStrg+I"

#: heksedit.rc:112
#, c-format
msgid "&Color settings"
msgstr "Farbeinstellungen"

#: heksedit.rc:114
#, c-format
msgid "&Text color...\tCtrl+T"
msgstr "Textfarbe...\tStrg+T"

#: heksedit.rc:115
#, c-format
msgid "&Background color...\tCtrl+K"
msgstr "Hintergrundfarbe...\tStrg+K"

#: heksedit.rc:116
#, c-format
msgid "Separator color...\tCtrl+Q"
msgstr "Trennzeichenfarbe...\tStrg+Q"

#: heksedit.rc:117
#, c-format
msgid "Selection text color...\tCtrl+Shift+T"
msgstr "Auswahltextfarbe...\tStrg+Umschalt+T"

#: heksedit.rc:119
#, c-format
msgid "Selection background color...\tCtrl+Shift+K"
msgstr "Auswahlhintergrundfarbe...\tStrg+Umschalt+K"

#: heksedit.rc:121
#, c-format
msgid "Bookmark color...\tCtrl+Shift+B"
msgstr "Lesezeichenfarbe...\tStrg+Umschalt+B"

#: heksedit.rc:123
#, c-format
msgid "Reset colors to standard...\tCtrl+Shift+S"
msgstr "Farben auf Standard zurücksetzen...\tStrg+Umschalt+S"

#: heksedit.rc:125
#, c-format
msgid "&Adopt OS colour scheme...\tCtrl+Shift+A"
msgstr "Farbschema des Betriebssystems übernehmen...\tStrg+Umschalt+A"

#: heksedit.rc:128
#, c-format
msgid "C&haracter set...\tCtrl+R"
msgstr "Zeichensatz...\tStrg+R"

#: heksedit.rc:129
#, c-format
msgid "B&inary mode...\tCtrl+Shift+Y"
msgstr "Binärmodus...\tStrg+Umschalt+Y"

#: heksedit.rc:130
#, c-format
msgid "&OLE Drag-drop...\tCtrl+Alt+O"
msgstr "OLE-Drag-and-Drop...\tStrg+Alt+O"

#: heksedit.rc:131
#, c-format
msgid "Make backups\tCtrl+Shift+M"
msgstr "Backups erstellen\tStrg+Umschalt+M"

#: heksedit.rc:133
#, c-format
msgid "&Registry"
msgstr "&Registrierung"

#: heksedit.rc:135
#, c-format
msgid "In &all context menu"
msgstr "In allen Kontextmenüs"

#: heksedit.rc:136
#, c-format
msgid "In &unknown context menu"
msgstr "Im unbekannten Kontextmenü"

#: heksedit.rc:137
#, c-format
msgid "Is &default in unknown context"
msgstr "Ist Standard im unbekannten Kontext"

#: heksedit.rc:138
#, c-format
msgid "&Save ini when required"
msgstr "Ini bei Bedarf speichern"

#: heksedit.rc:139
#, c-format
msgid "Change &instance..."
msgstr "Instanz ändern..."

#: heksedit.rc:140
#, c-format
msgid "S&hortcuts to Frhed..."
msgstr "Verknüpfung zu Frhed..."

#: heksedit.rc:142
#, c-format
msgid "&Bookmarks"
msgstr "Le&sezeichen"

#: heksedit.rc:144
#, c-format
msgid "&Add bookmark...\tCtrl+W"
msgstr "Lesezeichen hinzufügen\tStrg+W"

#: heksedit.rc:145
#, c-format
msgid "&Remove bookmark...\tAlt+W"
msgstr "Lesezeichen löschen\tAlt+W"

#: heksedit.rc:146
#, c-format
msgid "&Clear all bookmarks\tCtrl+Shift+W"
msgstr "Alle Lesezeichen löschen\tStrg+Umschalt+W"

#: heksedit.rc:158
#, c-format
msgid "&Misc"
msgstr "&Verschiedenes"

#: heksedit.rc:160
#, c-format
msgid "Goto DLL e&xport names..."
msgstr "Gehe zu DLL-Exportnamen..."

#: heksedit.rc:161
#, c-format
msgid "Goto DLL import names..."
msgstr "Gehe zu DLL-Importnamen..."

#: heksedit.rc:163
#, c-format
msgid "Encode / Decode..."
msgstr "Codieren/decodieren..."

#: heksedit.rc:165
#, c-format
msgid "&Help"
msgstr "&Hilfe"

#: heksedit.rc:167
#, c-format
msgid "&Help topics...\tF1"
msgstr "Hilfethemen...\tF1"

#: heksedit.rc:169
#, c-format
msgid "&About Frhed..."
msgstr "Über Frhed..."

#: heksedit.rc:183
#, c-format
msgid "Replace...\tCtrl+H"
msgstr "Ersetzen...\tStrg+H"

#: heksedit.rc:184
#, c-format
msgid "Find next\tF3"
msgstr "Weitersuchen\tF3"

#: heksedit.rc:185
#, c-format
msgid "Find previous\tShift+F3"
msgstr "Rückwärts suchen\tUmschalt+F3"

#: heksedit.rc:191
#, c-format
msgid "Goto &Next Sector"
msgstr "Gehe zu &nächstem Sektor"

#: heksedit.rc:192
#, c-format
msgid "Goto &Previous Sector"
msgstr "Gehe zu &vorherigem Sektor"

#: heksedit.rc:298
#, c-format
msgid "Go to Offset"
msgstr "Gehe zu Offset"

#: heksedit.rc:301
#, c-format
msgid "Start with \"+\" or \"-\" (minus) for relative jump."
msgstr "Mit \"+\" oder \"-\" für den relativen Sprung beginnen."

#: heksedit.rc:303
#, c-format
msgid "Prefix offset with \"x\" to indicate hex notation."
msgstr "Präfixoffset mit \"x\" zur Kennzeichnung der Hex-Notation."

#: heksedit.rc:306
#: heksedit.rc:347
#: heksedit.rc:367
#: heksedit.rc:387
#: heksedit.rc:412
#: heksedit.rc:430
#: heksedit.rc:448
#: heksedit.rc:486
#: heksedit.rc:498
#: heksedit.rc:533
#: heksedit.rc:549
#: heksedit.rc:563
#: heksedit.rc:577
#: heksedit.rc:593
#: heksedit.rc:606
#: heksedit.rc:652
#: heksedit.rc:675
#: heksedit.rc:689
#: heksedit.rc:750
#: heksedit.rc:772
#: heksedit.rc:793
#: heksedit.rc:819
#: heksedit.rc:877
#: heksedit.rc:898
#: heksedit.rc:946
#: heksedit.rc:977
#, c-format
msgid "OK"
msgstr "OK"

#: heksedit.rc:307
#: heksedit.rc:325
#: heksedit.rc:368
#: heksedit.rc:388
#: heksedit.rc:413
#: heksedit.rc:431
#: heksedit.rc:449
#: heksedit.rc:487
#: heksedit.rc:499
#: heksedit.rc:534
#: heksedit.rc:550
#: heksedit.rc:564
#: heksedit.rc:578
#: heksedit.rc:594
#: heksedit.rc:607
#: heksedit.rc:622
#: heksedit.rc:634
#: heksedit.rc:653
#: heksedit.rc:676
#: heksedit.rc:690
#: heksedit.rc:716
#: heksedit.rc:751
#: heksedit.rc:773
#: heksedit.rc:794
#: heksedit.rc:806
#: heksedit.rc:820
#: heksedit.rc:878
#: heksedit.rc:899
#: heksedit.rc:947
#: heksedit.rc:960
#: heksedit.rc:978
#: heksedit.rc:1332
#, c-format
msgid "Cancel"
msgstr "Abbrechen"

#: heksedit.rc:313
#: heksedit.rc:324
#: heksedit.rc:996
#, c-format
msgid "Find"
msgstr "Suchen"

#: heksedit.rc:316
#, c-format
msgid "&Find what:"
msgstr "Suchen nach:"

#: heksedit.rc:318
#, c-format
msgid "Please refer to the online help's Using the Special Syntax section for searching for binary values."
msgstr "Bitte beachten Sie in der Online-Hilfe den Abschnitt Verwendung der speziellen Syntax für die Suche nach binären Werten."

#: heksedit.rc:319
#, c-format
msgid "Direction"
msgstr "Richtung"

#: heksedit.rc:320
#, c-format
msgid "&Up"
msgstr "&Hoch"

#: heksedit.rc:321
#, c-format
msgid "&Down"
msgstr "&Runter"

#: heksedit.rc:322
#, c-format
msgid "Match &case"
msgstr "&Groß-/Kleinschreibung beachten"

#: heksedit.rc:323
#, c-format
msgid "U&nicode"
msgstr "&Unicode"

#: heksedit.rc:331
#, c-format
msgid "About Frhed"
msgstr "Über Frhed"

#: heksedit.rc:335
#, c-format
msgid "Frhed - Free hex editor"
msgstr "Frhed - Freier Hex-Editor"

#: heksedit.rc:338
#, c-format
msgid "(c) Raihan Kibria 2000"
msgstr "(c) Raihan Kibria 2000"

#: heksedit.rc:339
#, c-format
msgid "All rights reserved."
msgstr "Alle Rechte vorbehalten."

#: heksedit.rc:340
#, c-format
msgid "Contributors are listed in Contributors.txt."
msgstr "Mitwirkende sind in Contributors.txt aufgeführt."

#: heksedit.rc:342
#, c-format
msgid "Open contributors list"
msgstr "Liste der Mitwirkenden öffnen"

#: heksedit.rc:343
#, c-format
msgid "Homepage"
msgstr "Homepage"

#: heksedit.rc:345
#, c-format
msgid "Open Frhed's homepage in browser"
msgstr "Frhed-Homepage im Browser öffnen"

#: heksedit.rc:353
#, c-format
msgid "Export Hexdump"
msgstr "Hexdump exportieren"

#: heksedit.rc:356
#, c-format
msgid "Start at offset:"
msgstr "Start bei Offset:"

#: heksedit.rc:358
#, c-format
msgid "End at offset:"
msgstr "Ende bei Offset:"

#: heksedit.rc:360
#, c-format
msgid "Export to"
msgstr "Exportieren nach"

#: heksedit.rc:361
#, c-format
msgid "File"
msgstr "Datei"

#: heksedit.rc:362
#, c-format
msgid "Clipboard"
msgstr "Zwischenablage"

#: heksedit.rc:363
#, c-format
msgid "Type"
msgstr "Typ"

#: heksedit.rc:364
#, c-format
msgid "Frhed display as text"
msgstr "Frhed-Anzeige als Text"

#: heksedit.rc:365
#, c-format
msgid "Just hex digits on a line"
msgstr "Nur Hexadezimalziffern in einer Zeile"

#: heksedit.rc:366
#, c-format
msgid "Frhed display as RTF"
msgstr "Frhed-Anzeige als RTF"

#: heksedit.rc:374
#, c-format
msgid "Enter decimal value"
msgstr "Dezimalwert eingeben"

#: heksedit.rc:377
#, c-format
msgid "Enter decimal value:"
msgstr "Dezimalwert eingeben:"

#: heksedit.rc:379
#, c-format
msgid "At offset:"
msgstr "Bei Offset:"

#: heksedit.rc:381
#, c-format
msgid "Number of times:"
msgstr "Anzahl:"

#: heksedit.rc:383
#, c-format
msgid "Size of Value:"
msgstr "Größe des Wertes:"

#: heksedit.rc:384
#, c-format
msgid "Byte (8 bit)"
msgstr "Byte (8 Bit)"

#: heksedit.rc:385
#, c-format
msgid "Word (16 bit)"
msgstr "Word (16 Bit)"

#: heksedit.rc:386
#, c-format
msgid "Double word (32 bit)"
msgstr "Doppelwort (32 Bit)"

#: heksedit.rc:394
#, c-format
msgid "Paste with dialogue"
msgstr "Einfügen mit Dialog"

#: heksedit.rc:397
#, c-format
msgid "Clipboard content (text):"
msgstr "Inhalt der Zwischenablage (Text):"

#: heksedit.rc:400
#: heksedit.rc:662
#, c-format
msgid "Paste mode"
msgstr "Einfügemodus"

#: heksedit.rc:401
#: heksedit.rc:663
#, c-format
msgid "Overwrite"
msgstr "Überschreiben"

#: heksedit.rc:403
#: heksedit.rc:664
#, c-format
msgid "Insert"
msgstr "Einfügen"

#: heksedit.rc:405
#: heksedit.rc:665
#, c-format
msgid "Paste data how many times:"
msgstr "Daten wie oft einfügen:"

#: heksedit.rc:407
#: heksedit.rc:669
#, c-format
msgid "Paste coded binary values as text"
msgstr "Kodierte binäre Werte als Text einfügen"

#: heksedit.rc:409
#, c-format
msgid "Skip how many bytes between inserts/overwrites"
msgstr "Wie viele Bytes zwischen Einfügen/Überschreiben überspringen"

#: heksedit.rc:419
#: heksedit.rc:1000
#, c-format
msgid "Cut"
msgstr "Ausschneiden"

#: heksedit.rc:422
#, c-format
msgid "Start cutting at offset:"
msgstr "Ausschneiden starten bei Offset:"

#: heksedit.rc:424
#, c-format
msgid "Cut how many bytes"
msgstr "Wie viele Bytes ausschneiden"

#: heksedit.rc:425
#, c-format
msgid "Cut up to and including offset:"
msgstr "Ausschneiden bis einschließlich Offset:"

#: heksedit.rc:427
#, c-format
msgid "Number of bytes to cut:"
msgstr "Anzahl Bytes zum Ausschneiden:"

#: heksedit.rc:437
#: heksedit.rc:865
#: heksedit.rc:896
#: heksedit.rc:997
#: heksedit.rc:1331
#, c-format
msgid "Copy"
msgstr "Kopieren"

#: heksedit.rc:440
#, c-format
msgid "Start copying at offset:"
msgstr "Kopieren starten bei Offset:"

#: heksedit.rc:442
#, c-format
msgid "Copy how many bytes"
msgstr "Wie viele Bytes kopieren"

#: heksedit.rc:443
#, c-format
msgid "Copy up to and including offset:"
msgstr "Kopieren bis einschließlich Offset:"

#: heksedit.rc:445
#, c-format
msgid "Number of bytes to copy:"
msgstr "Anzahl Bytes zum Kopieren:"

#: heksedit.rc:455
#, c-format
msgid "View Settings"
msgstr "Einstellungen"

#: heksedit.rc:458
#, c-format
msgid "Number of bytes to display per line:"
msgstr "Anzahl der pro Zeile anzuzeigenden Bytes:"

#: heksedit.rc:461
#, c-format
msgid "Automatically adjust number of bytes per line (uncheck this if you want Frhed to use your own choice for bytes per line)"
msgstr "Anzahl der Bytes pro Zeile automatisch anpassen (Option deaktivieren, wenn Frhed Ihre eigene Wahl für Bytes pro Zeile verwenden soll.)"

#: heksedit.rc:464
#, c-format
msgid "Display length of offset in how many characters:"
msgstr "Anzahl der Zeichen für Offset-Länge:"

#: heksedit.rc:467
#, c-format
msgid "Adjust offset length to that of the max offset"
msgstr "Offset-Länge an die des maximalen Offsets anpassen"

#: heksedit.rc:470
#, c-format
msgid "Display values at caret position as:"
msgstr "Werte an der Caret-Position anzeigen"

#: heksedit.rc:472
#, c-format
msgid "unsigned"
msgstr "ohne Vorzeichen"

#: heksedit.rc:474
#, c-format
msgid "signed"
msgstr "mit Vorzeichen"

#: heksedit.rc:476
#, c-format
msgid "Set read-only mode on opening files"
msgstr "Schreibschutz-Modus beim Öffnen von Dateien setzen"

#: heksedit.rc:479
#, c-format
msgid "Path and filename of the text editor to call:"
msgstr "Pfad und Dateiname des aufzurufenden Texteditors:"

#: heksedit.rc:482
#, c-format
msgid "Browse..."
msgstr "Suchen..."

#: heksedit.rc:483
#, c-format
msgid "Language:"
msgstr "Sprache:"

#: heksedit.rc:493
#, c-format
msgid "Append"
msgstr "Anhängen"

#: heksedit.rc:496
#, c-format
msgid "Bytes to append to the end of the file:"
msgstr "An das Ende der Datei anzuhängende Bytes:"

#: heksedit.rc:504
#, c-format
msgid "Manipulate Bits"
msgstr "Bits manipulieren"

#: heksedit.rc:508
#: heksedit.rc:528
#, c-format
msgid "8"
msgstr "8"

#: heksedit.rc:509
#, c-format
msgid "7"
msgstr "7"

#: heksedit.rc:510
#, c-format
msgid "6"
msgstr "6"

#: heksedit.rc:511
#, c-format
msgid "5"
msgstr "5"

#: heksedit.rc:512
#: heksedit.rc:529
#, c-format
msgid "4"
msgstr "4"

#: heksedit.rc:513
#, c-format
msgid "3"
msgstr "3"

#: heksedit.rc:514
#: heksedit.rc:530
#, c-format
msgid "2"
msgstr "2"

#: heksedit.rc:524
#, c-format
msgid "128"
msgstr "128"

#: heksedit.rc:525
#, c-format
msgid "64"
msgstr "64"

#: heksedit.rc:526
#, c-format
msgid "32"
msgstr "32"

#: heksedit.rc:527
#, c-format
msgid "16"
msgstr "16"

#: heksedit.rc:540
#, c-format
msgid "Character Set"
msgstr "Zeichensatz"

#: heksedit.rc:543
#, c-format
msgid "Choose character set"
msgstr "Zeichensatz auswählen"

#: heksedit.rc:544
#: heksedit.rc:1024
#, c-format
msgid "ANSI"
msgstr "ANSI"

#: heksedit.rc:546
#: heksedit.rc:1025
#, c-format
msgid "OEM"
msgstr "OEM"

#: heksedit.rc:547
#, c-format
msgid "Font size in points:"
msgstr "Schriftgröße in Punkt:"

#: heksedit.rc:556
#, c-format
msgid "Choose area of difference to display"
msgstr "Wählen Sie den anzuzeigenden Differenzbereich aus"

#: heksedit.rc:559
#, c-format
msgid "file_sizes"
msgstr "file_sizes"

#: heksedit.rc:560
#, c-format
msgid "number_of_diffs"
msgstr "number_of_diffs"

#: heksedit.rc:565
#, c-format
msgid "Copy above list"
msgstr "Obige Liste kopieren"

#: heksedit.rc:571
#, c-format
msgid "Binary Mode"
msgstr "Binärmodus"

#: heksedit.rc:574
#, c-format
msgid "Choose binary mode"
msgstr "Binärmodus auswählen"

#: heksedit.rc:575
#, c-format
msgid "Little-endian (Intel)"
msgstr "Little-endian (Intel)"

#: heksedit.rc:576
#, c-format
msgid "Big-endian (Motorola)"
msgstr "Big-endian (Motorola)"

#: heksedit.rc:584
#, c-format
msgid "Select Block"
msgstr "Block auswählen"

#: heksedit.rc:587
#, c-format
msgid "Start of selection (prefix x for hex):"
msgstr "Beginn der Auswahl (Präfix x für hex):"

#: heksedit.rc:590
#, c-format
msgid "End of selection (prefix x for hex):"
msgstr "Ende der Auswahl (Präfix x für hex):"

#: heksedit.rc:599
#, c-format
msgid "Reverse Bytes"
msgstr "Bytes umkehren"

#: heksedit.rc:602
#, c-format
msgid "Offset of first byte:"
msgstr "Offset des ersten Bytes:"

#: heksedit.rc:604
#, c-format
msgid "Offset of last byte:"
msgstr "Offset des letzten Bytes:"

#: heksedit.rc:613
#, c-format
msgid "Add Bookmark"
msgstr "Lesezeichen hinzufügen"

#: heksedit.rc:616
#, c-format
msgid "Offset (prefix x for hex):"
msgstr "Offset (Präfix x für hex):"

#: heksedit.rc:619
#, c-format
msgid "Name:"
msgstr "Name:"

#: heksedit.rc:621
#, c-format
msgid "Add"
msgstr "Hinzufügen"

#: heksedit.rc:628
#, c-format
msgid "Remove Bookmark"
msgstr "Lesezeichen entfernen"

#: heksedit.rc:633
#, c-format
msgid "Remove"
msgstr "Entfernen"

#: heksedit.rc:640
#, c-format
msgid "Open partially"
msgstr "Teilweise öffnen"

#: heksedit.rc:643
#, c-format
msgid "Start opening partially"
msgstr "Teilweise Öffnung beginnen"

#: heksedit.rc:644
#, c-format
msgid "&At offset from the beginning of file (prefix x for hex):"
msgstr "Beim Offset vom Dateianfang (Präfix x für hex):"

#: heksedit.rc:646
#, c-format
msgid "&From end of file"
msgstr "Vom Dateiende"

#: heksedit.rc:648
#, c-format
msgid "&How many bytes:"
msgstr "Wie viele Bytes:"

#: heksedit.rc:650
#, c-format
msgid "&Display offsets relative to start of file (or start of loaded block)"
msgstr "Offsets relativ zum Dateistart anzeigen (oder zum Start des geladenen Blocks)"

#: heksedit.rc:659
#: heksedit.rc:998
#, c-format
msgid "Paste"
msgstr "Einfügen"

#: heksedit.rc:667
#, c-format
msgid "Bytes to skip between inserts/overwrites:"
msgstr "Bytes zum Überspringen zwischen Einfügen/Überschreiben:"

#: heksedit.rc:671
#, c-format
msgid "Clipboard format to use:"
msgstr "Format für die Zwischenablage:"

#: heksedit.rc:673
#, c-format
msgid "Refresh"
msgstr "Aktualisieren"

#: heksedit.rc:674
#, c-format
msgid "- CF_TEXT was used by previous versions of Frhed\n- Those starting w CF_ are standard formats && others are registered/private ones\n- Some formats may have been synthesized from the original."
msgstr "- CF_TEXT wurde von früheren Versionen von Frhed verwendet.\n- Die mit w CF_ beginnenden Formate sind Standardformate, andere sind registrierte/private Formate.\n- Einige Formate wurden möglicherweise aus dem Original synthetisiert."

#: heksedit.rc:682
#, c-format
msgid "Template"
msgstr "Vorlage"

#: heksedit.rc:685
#, c-format
msgid "Result of template application:"
msgstr "Ergebnis der Vorlagenanwendung:"

#: heksedit.rc:696
#: heksedit.rc:715
#: heksedit.rc:1005
#, c-format
msgid "Replace"
msgstr "Ersetzen"

#: heksedit.rc:699
#, c-format
msgid "Find what:"
msgstr "Suchen nach:"

#: heksedit.rc:702
#, c-format
msgid "Find next"
msgstr "Vorwärts suchen"

#: heksedit.rc:703
#, c-format
msgid "Find previous"
msgstr "Rückwärts suchen"

#: heksedit.rc:704
#, c-format
msgid "Match case"
msgstr "Groß-/Kleinschreibung beachten"

#: heksedit.rc:706
#, c-format
msgid "Replace with:"
msgstr "Ersetzen durch:"

#: heksedit.rc:708
#, c-format
msgid "Replace all..."
msgstr "Ersetzen aller..."

#: heksedit.rc:709
#, c-format
msgid "... following occurrences"
msgstr "... nachfolgenden Vorkommen"

#: heksedit.rc:711
#, c-format
msgid "... previous occurrences"
msgstr "... vorangehenden Vorkommen"

#: heksedit.rc:713
#, c-format
msgid "Use special syntax"
msgstr "Spezielle Syntax verwenden"

#: heksedit.rc:721
#, c-format
msgid "Fill Selection"
msgstr "Auswahl ausfüllen"

#: heksedit.rc:724
#, c-format
msgid "Fill from:"
msgstr "Ausfüllen von:"

#: heksedit.rc:726
#: heksedit.rc:1298
#, c-format
msgid "File:"
msgstr "Datei:"

#: heksedit.rc:728
#, c-format
msgid "&Browse..."
msgstr "Suchen..."

#: heksedit.rc:729
#, c-format
msgid "Hex:"
msgstr "Hex:"

#: heksedit.rc:731
#, c-format
msgid "Selection"
msgstr "Auswahl"

#: heksedit.rc:732
#, c-format
msgid "Start:"
msgstr "Start:"

#: heksedit.rc:734
#, c-format
msgid "End:"
msgstr "Ende:"

#: heksedit.rc:736
#, c-format
msgid "Size:"
msgstr "Größe:"

#: heksedit.rc:738
#: heksedit.rc:920
#, c-format
msgid "Input"
msgstr "Eingabe"

#: heksedit.rc:739
#, c-format
msgid "Size of input:"
msgstr "Größe der Eingabe:"

#: heksedit.rc:741
#, c-format
msgid "# Times input fits:"
msgstr "# Mal passt die Eingabe:"

#: heksedit.rc:743
#, c-format
msgid "Remainder:"
msgstr "Rest:"

#: heksedit.rc:745
#, c-format
msgid "Assignment operator"
msgstr "Aufgabenverwalter"

#: heksedit.rc:746
#, c-format
msgid "Assign (=)"
msgstr "Zuweisen (=)"

#: heksedit.rc:747
#, c-format
msgid "OR (|=)"
msgstr "OR (|=)"

#: heksedit.rc:748
#, c-format
msgid "AND (&&=)"
msgstr "AND (&&=)"

#: heksedit.rc:749
#, c-format
msgid "XOR (^=)"
msgstr "XOR (^=)"

#: heksedit.rc:757
#, c-format
msgid "Change Instance"
msgstr "Instanz wechseln"

#: heksedit.rc:760
#, c-format
msgid "Load the following instance data:"
msgstr "Folgende Instanzdaten laden:"

#: heksedit.rc:766
#, c-format
msgid "and save (when quiting) to this instance data:"
msgstr "und in dieser Instanz Daten (beim Beenden) speichern:"

#: heksedit.rc:774
#, c-format
msgid "Remember that if any instance of Frhed is running on the same instance data as this one it will overwrite the data of this one if it exits last.  Unless, that is, that the option to save ini data to the registry on exit is disabled)"
msgstr "Wenn eine Instanz von Frhed auf denselben Instanzdaten wie diese läuft, werden die Daten dieser Instanz überschrieben werden, wenn sie zuletzt beendet wird. Es sei denn, die Option zum Speichern von INI-Daten in der Registrierung beim Beenden ist deaktiviert."

#: heksedit.rc:781
#, c-format
msgid "Encode / Decode Data"
msgstr "Daten codieren/decodieren"

#: heksedit.rc:784
#, c-format
msgid "Algorithms:"
msgstr "Algorithmen:"

#: heksedit.rc:787
#, c-format
msgid "Arguments:"
msgstr "Argumente:"

#: heksedit.rc:789
#, c-format
msgid "Encode"
msgstr "Codieren"

#: heksedit.rc:791
#, c-format
msgid "Decode"
msgstr "Decodieren"

#: heksedit.rc:800
#, c-format
msgid "Open Drive"
msgstr "Laufwerk öffnen"

#: heksedit.rc:804
#, c-format
msgid "NOTE: Frhed allows only reading the drive data. Changing and saving the drive data is not possible."
msgstr "HINWEIS: Frhed erlaubt nur das Lesen der Lauf- werksdaten. Ändern u. Speichern sind nicht möglich."

#: heksedit.rc:805
#: heksedit.rc:959
#: heksedit.rc:989
#, c-format
msgid "Open"
msgstr "Öffnen"

#: heksedit.rc:812
#, c-format
msgid "Go to Track"
msgstr "Gehe zu Spur"

#: heksedit.rc:815
#, c-format
msgid "Drive Information:"
msgstr "Laufwerksinformation:"

#: heksedit.rc:817
#, c-format
msgid "&Go to track:"
msgstr "Gehe zu Spur:"

#: heksedit.rc:826
#, c-format
msgid "Shortcuts"
msgstr "Verknüpfungen"

#: heksedit.rc:829
#, c-format
msgid "Paths of shortcuts to Frhed:"
msgstr "Verknüpfungspfad zu Frhed:"

#: heksedit.rc:830
#, c-format
msgid "Links to Frhed"
msgstr "Links zu Frhed"

#: heksedit.rc:833
#, c-format
msgid "&Find links to any Frhed.exe, fix them and list them here."
msgstr "Links zu jeder Frhed.exe finden, reparieren und hier auflisten."

#: heksedit.rc:836
#, c-format
msgid "Create link in/on:"
msgstr "Link erstellen in/auf:"

#: heksedit.rc:837
#, c-format
msgid "&Desktop"
msgstr "Desktop"

#: heksedit.rc:839
#, c-format
msgid "S&tart Menu"
msgstr "Startmenü"

#: heksedit.rc:841
#, c-format
msgid "&Send To"
msgstr "Senden an"

#: heksedit.rc:843
#, c-format
msgid "&Programs"
msgstr "Programme"

#: heksedit.rc:845
#, c-format
msgid "Link management:"
msgstr "Linkverwaltung:"

#: heksedit.rc:846
#, c-format
msgid "<< &Add..."
msgstr "<< Hinzufügen..."

#: heksedit.rc:847
#, c-format
msgid "&Update list"
msgstr "Liste aktualisieren"

#: heksedit.rc:849
#, c-format
msgid ">> &Delete"
msgstr ">> Löschen"

#: heksedit.rc:850
#, c-format
msgid "&Move"
msgstr "Verschieben"

#: heksedit.rc:852
#, c-format
msgid "&Reload"
msgstr "Neu laden"

#: heksedit.rc:853
#, c-format
msgid "&Close"
msgstr "Schließen"

#: heksedit.rc:859
#, c-format
msgid "Move/Copy bytes"
msgstr "Bytes verschieben/kopieren"

#: heksedit.rc:862
#, c-format
msgid "Operation:"
msgstr "Operation:"

#: heksedit.rc:863
#, c-format
msgid "Start with (-) for negative length or movement backwards. Prefix with x for hex notation."
msgstr "Mit (-) für negative Länge oder Bewegung rückwärts beginnen. Präfix mit x für hexadezimale Notation."

#: heksedit.rc:864
#: heksedit.rc:894
#: heksedit.rc:1330
#, c-format
msgid "Move"
msgstr "Verschieben"

#: heksedit.rc:866
#, c-format
msgid "Begin of the block:"
msgstr "Blockbeginn:"

#: heksedit.rc:868
#, c-format
msgid "End of the block:"
msgstr "Blockende:"

#: heksedit.rc:870
#, c-format
msgid "Offset"
msgstr "Offset"

#: heksedit.rc:871
#, c-format
msgid "Length"
msgstr "Länge"

#: heksedit.rc:872
#, c-format
msgid "Target:"
msgstr "Ziel:"

#: heksedit.rc:874
#, c-format
msgid "Bytes forward"
msgstr "Bytes vorwärts"

#: heksedit.rc:875
#, c-format
msgid "First byte offset"
msgstr "Offset des ersten Bytes"

#: heksedit.rc:876
#, c-format
msgid "Last byte offset"
msgstr "Offset des letzten Bytes"

#: heksedit.rc:884
#, c-format
msgid "Drag-drop"
msgstr "Drag-and-Drop"

#: heksedit.rc:887
#, c-format
msgid "Clipboard format[s] to use:"
msgstr "Zu verwendende Zwischenablageformate:"

#: heksedit.rc:891
#, c-format
msgid "Up"
msgstr "Hoch"

#: heksedit.rc:892
#, c-format
msgid "Down"
msgstr "Runter"

#: heksedit.rc:893
#, c-format
msgid "Data:"
msgstr "Daten:"

#: heksedit.rc:905
#, c-format
msgid "OLE Drag-drop options"
msgstr "OLE-Drag-and-drop-Optionen"

#: heksedit.rc:908
#, c-format
msgid "General"
msgstr "Allgemein"

#: heksedit.rc:909
#, c-format
msgid "Enable drop input"
msgstr "Drop-Eingang aktivieren"

#: heksedit.rc:911
#, c-format
msgid "Enable drag output"
msgstr "Drag-Ausgang aktivieren"

#: heksedit.rc:913
#, c-format
msgid "Enable scroll-delaying for drag-drop"
msgstr "Scroll-Verzögerung für Drag-Drop aktivieren"

#: heksedit.rc:915
#, c-format
msgid "Enable scroll-delaying for selecting"
msgstr "Scroll-Verzögerung für die Auswahl aktivieren"

#: heksedit.rc:917
#, c-format
msgid "Always give the opportunity to change between move and copy after a drop."
msgstr "Immer die Möglichkeit geben, nach dem Ablegen zwischen Verschieben und Kopieren zu wechseln."

#: heksedit.rc:921
#, c-format
msgid "CF_HDROP opens the files dropped."
msgstr "CF_HDROP öffnet die abgelegten Dateien."

#: heksedit.rc:923
#, c-format
msgid "Drop \"BinaryData\" if present"
msgstr "\"BinaryData\" löschen, falls vorhanden"

#: heksedit.rc:926
#, c-format
msgid "Drop CF_TEXT if present"
msgstr "CF_TEXT löschen, falls vorhanden"

#: heksedit.rc:929
#, c-format
msgid "Output"
msgstr "Ausgabe"

#: heksedit.rc:930
#, c-format
msgid "Drag \"BinaryData\""
msgstr "\"BinaryData\" ziehen"

#: heksedit.rc:933
#, c-format
msgid "Drag CF_TEXT as:"
msgstr "CF_TEXT ziehen als:"

#: heksedit.rc:936
#, c-format
msgid "Hexdump"
msgstr "Hexdump"

#: heksedit.rc:938
#, c-format
msgid "Special syntax (<bh:ff>...)"
msgstr "Spezielle Syntax (<bh:ff>...)"

#: heksedit.rc:940
#, c-format
msgid "display (else digits)"
msgstr "anzeigen (andere Ziffern)"

#: heksedit.rc:943
#, c-format
msgid "Drag \"Rich Text Format\" (as hexdump)"
msgstr "\"Rich Text Format\" ziehen (als Hexdump)"

#: heksedit.rc:953
#, c-format
msgid "Multiple files dropped"
msgstr "Mehrere Dateien fallengelassen"

#: heksedit.rc:956
#, c-format
msgid "Select the file to open:"
msgstr "Zu öffnende Datei auswählen:"

#: heksedit.rc:966
#, c-format
msgid "Delete"
msgstr "Löschen"

#: heksedit.rc:969
#, c-format
msgid "Start deleting at offset:"
msgstr "Löschen beginnen am Offset:"

#: heksedit.rc:971
#, c-format
msgid "Delete how many bytes"
msgstr "Wie viele Bytes löschen"

#: heksedit.rc:972
#, c-format
msgid "Delete up to and including offset:"
msgstr "Löschen bis einschließlich Offset:"

#: heksedit.rc:974
#, c-format
msgid "Number of bytes to delete:"
msgstr "Anzahl Bytes zum Löschen:"

#: heksedit.rc:990
#, c-format
msgid "New"
msgstr "Neu"

#: heksedit.rc:995
#, c-format
msgid "Save"
msgstr "Speichern"

#: heksedit.rc:999
#, c-format
msgid "Help"
msgstr "Hilfe"

#: heksedit.rc:1010
#, c-format
msgid "Goto next sector"
msgstr "Gehe zum nächsten Sektor"

#: heksedit.rc:1011
#, c-format
msgid "Goto previous sector"
msgstr "Gehe zum vorherigen Sektor"

#: heksedit.rc:1012
#, c-format
msgid "Goto first sector"
msgstr "Gehe zum ersten Sektor"

#: heksedit.rc:1013
#, c-format
msgid "Goto last sector"
msgstr "Gehe zum letzten Sektor"

#: heksedit.rc:1019
#, c-format
msgid "Frhed"
msgstr "Frhed"

#: heksedit.rc:1020
#, c-format
msgid "Version %u.%u.%u"
msgstr "Version %u.%u.%u"

#: heksedit.rc:1021
#, c-format
msgid "%d) 0x%zx=%zu to 0x%zx=%zu (%zu bytes)"
msgstr "%d) 0x%zx=%zu bis 0x%zx=%zu (%zu Bytes)"

#: heksedit.rc:1022
#, c-format
msgid "All Files (*.*)|*.*|"
msgstr "Alle Dateien (*.*)|*.*|"

#: heksedit.rc:1023
#, c-format
msgid "Untitled"
msgstr "Ohne Titel"

#: heksedit.rc:1026
#, c-format
msgid "READ"
msgstr "RO"

#: heksedit.rc:1027
#, c-format
msgid "OVR"
msgstr "Üb"

#: heksedit.rc:1028
#, c-format
msgid "INS"
msgstr "Einfg"

#: heksedit.rc:1029
#: heksedit.rc:1290
#, c-format
msgid "L"
msgstr "L"

#: heksedit.rc:1030
#: heksedit.rc:1287
#, c-format
msgid "B"
msgstr "B"

#: heksedit.rc:1031
#, c-format
msgid "You are trying to open a link file.\nClick on Yes if you want to open the file linked to,\nor click on No if you want to open the link file itself.\nChoose Cancel if you want to abort opening."
msgstr "Sie versuchen, eine Verknüpfungsdatei zu öffnen.\nKlicken Sie auf Ja, wenn Sie die verknüpfte Datei öffnen möchten,\noder klicken Sie auf Nein, wenn Sie die Verknüpfungsdatei selbst öffnen möchten.\nWählen Sie Abbrechen, wenn Sie das Öffnen abbrechen möchten."

#: heksedit.rc:1037
#, c-format
msgid "Start offset not recognized."
msgstr "Offset-Start nicht erkannt."

#: heksedit.rc:1038
#, c-format
msgid "End offset not recognized."
msgstr "Offset-Ende nicht erkannt."

#: heksedit.rc:1039
#, c-format
msgid "Offset not recognized."
msgstr "Offset nicht erkannt."

#: heksedit.rc:1045
#, c-format
msgid "Error while reading from file."
msgstr "Fehler beim Lesen aus Datei."

#: heksedit.rc:1046
#, c-format
msgid "Not enough memory."
msgstr "Nicht genug Speicher."

#: heksedit.rc:1047
#, c-format
msgid "Number of bytes not recognized."
msgstr "Anzahl der nicht erkannten Bytes."

#: heksedit.rc:1048
#, c-format
msgid "Error opening file."
msgstr "Fehler beim Öffnen der Datei."

#: heksedit.rc:1049
#, c-format
msgid "Not enough memory to load file."
msgstr "Nicht genug Speicher zum Laden der Datei."

#: heksedit.rc:1050
#, c-format
msgid "Could not save the file."
msgstr "Konnte die Datei nicht speichern."

#: heksedit.rc:1051
#, c-format
msgid "Could not backup file!\nBackup aborted, Save continuing."
msgstr "Konnte Datei nicht sichern!\nBackup abgebrochen, Speichern wird fortgesetzt."

#: heksedit.rc:1052
#, c-format
msgid "Could not save partially opened file."
msgstr "Konnte teilweise geöffnete Datei nicht speichern."

#: heksedit.rc:1053
#, c-format
msgid "Could not move data in the file."
msgstr "Daten in der Datei konnten nicht verschoben werden."

#: heksedit.rc:1054
#, c-format
msgid "Could not resize the file."
msgstr "Konnte die Größe der Datei nicht ändern."

#: heksedit.rc:1055
#, c-format
msgid "Could not seek in file."
msgstr "Konnte nicht in Datei suchen."

#: heksedit.rc:1056
#, c-format
msgid "Could not write data to file."
msgstr "Konnte keine Daten in die Datei schreiben."

#: heksedit.rc:1057
#, c-format
msgid "Help file\n%s\nnot found!"
msgstr "Hilfedatei\n%s\nnicht gefunden!"

#: heksedit.rc:1058
#, c-format
msgid "Error code 0x%x occurred while opening file %s."
msgstr "Fehlercode 0x%x trat beim Öffnen der Datei %s auf."

#: heksedit.rc:1059
#, c-format
msgid "Could not save preferences to registry."
msgstr "Konnte die Einstellungen nicht in der Registrierung speichern."

#: heksedit.rc:1060
#, c-format
msgid "File is empty."
msgstr "Datei ist leer."

#: heksedit.rc:1061
#, c-format
msgid "An error occurred when calling the text editor."
msgstr "Beim Aufruf des Texteditors ist ein Fehler aufgetreten."

#: heksedit.rc:1062
#, c-format
msgid "Could not insert data."
msgstr "Konnte keine Daten einfügen."

#: heksedit.rc:1063
#, c-format
msgid "Error checking file size"
msgstr "Fehler bei der Überprüfung der Dateigröße"

#: heksedit.rc:1064
#, c-format
msgid "No data present. Cannot continue!"
msgstr "Keine Daten vorhanden. Kann nicht fortfahren!"

#: heksedit.rc:1065
#, c-format
msgid "Could not get text from the file.\nCannot continue!"
msgstr "Es konnte kein Text aus der Datei abgerufen werden. Kann nicht fortfahren!"

#: heksedit.rc:1071
#, c-format
msgid "KB"
msgstr "KB"

#: heksedit.rc:1072
#, c-format
msgid "MB"
msgstr "MB"

#: heksedit.rc:1073
#, c-format
msgid "GB"
msgstr "GB"

#: heksedit.rc:1074
#, c-format
msgid "TB"
msgstr "TB"

#: heksedit.rc:1080
#, c-format
msgid "Could not open the browser."
msgstr "Konnte den Browser nicht öffnen."

#: heksedit.rc:1081
#, c-format
msgid "File\n%s\nnot found!"
msgstr "Datei\n%s\nnicht gefunden!"

#: heksedit.rc:1087
#, c-format
msgid "Can not set bookmark at that position."
msgstr "Kann an dieser Position kein Lesezeichen setzen."

#: heksedit.rc:1088
#, c-format
msgid "There already is a bookmark on that position."
msgstr "Für diese Position gibt es bereits ein Lesezeichen."

#: heksedit.rc:1089
#, c-format
msgid "Can not set bookmark in empty file."
msgstr "Kann kein Lesezeichen in einer leeren Datei setzen."

#: heksedit.rc:1090
#, c-format
msgid "Can not set any more bookmarks."
msgstr "Kann keine weiteren Lesezeichen setzen."

#: heksedit.rc:1091
#, c-format
msgid "Bookmark points to invalid position."
msgstr "Das Lesezeichen zeigt auf eine ungültige Position."

#: heksedit.rc:1092
#, c-format
msgid "No bookmarks to remove."
msgstr "Keine Lesezeichen zu entfernen."

#: heksedit.rc:1093
#, c-format
msgid "Remove all bookmarks?"
msgstr "Alle Lesezeichen löschen?"

#: heksedit.rc:1099
#, c-format
msgid "Number of bytes to append not recognized."
msgstr "Anzahl der anzuhängenden Bytes nicht erkannt."

#: heksedit.rc:1100
#, c-format
msgid "Not enough memory for appending."
msgstr "Nicht genügend Speicherplatz zum Anhängen."

#: heksedit.rc:1106
#, c-format
msgid "Offset: 0x%zx = %zd"
msgstr "Offset: 0x%zx = %zd"

#: heksedit.rc:1107
#, c-format
msgid "Value: 0x%02x, %d signed, %u unsigned."
msgstr "Wert: 0x%02x, %d mit Vorzeichen, %u ohne Vorzeichen."

#: heksedit.rc:1113
#, c-format
msgid "Choose file to compare with"
msgstr "Datei wählen zum Vergleich mit"

#: heksedit.rc:1114
#, c-format
msgid "Error while opening file."
msgstr "Fehler beim Öffnen der Datei."

#: heksedit.rc:1115
#, c-format
msgid "%d areas of difference found."
msgstr "%d Bereiche mit Unterschieden gefunden."

#: heksedit.rc:1116
#, c-format
msgid "Remaining loaded data size: %lld, size of file on disk: %lld."
msgstr "Verbleibende Größe der geladenen Daten: %lld, Größe der Datei auf der Festplatte: %lld."

#: heksedit.rc:1117
#, c-format
msgid "Data matches exactly."
msgstr "Daten stimmen genau überein."

#: heksedit.rc:1123
#, c-format
msgid "Cannot get access to clipboard."
msgstr "Kein Zugriff auf die Zwischenablage möglich."

#: heksedit.rc:1124
#, c-format
msgid "Cannot lock clipboard."
msgstr "Die Zwischenablage kann nicht gesperrt werden."

#: heksedit.rc:1125
#: heksedit.rc:1136
#, c-format
msgid "Not enough memory for copying."
msgstr "Nicht genug Speicher zum Kopieren."

#: heksedit.rc:1126
#, c-format
msgid "\nDo you want the above output to be copied to the clipboard?\n"
msgstr "\nMöchten Sie, dass die obige Ausgabe in die Zwischenablage kopiert wird?\n"

#: heksedit.rc:1127
#, c-format
msgid "You need to select a clipboard format to use."
msgstr "Sie müssen ein Format für die Zwischenablage auswählen."

#: heksedit.rc:1128
#, c-format
msgid "Could not get text from the clipboard.\nCannot continue!"
msgstr "Konnte keinen Text aus der Zwischenablage erhalten.\nKann nicht fortfahren!"

#: heksedit.rc:1129
#, c-format
msgid "There is text on the clipboard.\nDo you want to import from\nthe clipboard instead of a file?"
msgstr "Es befindet sich Text in der Zwischenablage.\nWollen Sie aus der Zwischenablage\nstatt aus einer Datei importieren?"

#: heksedit.rc:1135
#, c-format
msgid "Can't copy more bytes than are present."
msgstr "Es können nicht mehr Bytes kopiert werden, als vorhanden sind."

#: heksedit.rc:1142
#, c-format
msgid "Can't cut more bytes than are present."
msgstr "Es können nicht mehr Bytes ausgeschnitten werden, als vorhanden sind."

#: heksedit.rc:1143
#, c-format
msgid "Not enough memory for cutting to clipboard."
msgstr "Nicht genügend Speicher für das Ausschneiden in die Zwischenablage."

#: heksedit.rc:1144
#, c-format
msgid "Could not cut the data."
msgstr "Konnte die Daten nicht kürzen."

#: heksedit.rc:1150
#, c-format
msgid "Can't delete more bytes than are present."
msgstr "Es können nicht mehr Bytes gelöscht werden, als vorhanden sind."

#: heksedit.rc:1151
#, c-format
msgid "Could not delete data."
msgstr "Konnte Daten nicht löschen."

#: heksedit.rc:1157
#, c-format
msgid "Decimal value not recognized."
msgstr "Dezimalwert nicht erkannt."

#: heksedit.rc:1158
#, c-format
msgid "Number of times not recognized."
msgstr "Anzahl der nicht erkannten Bytes."

#: heksedit.rc:1159
#, c-format
msgid "Invalid start offset."
msgstr "Ungültiger Start-Offset."

#: heksedit.rc:1160
#, c-format
msgid "Not enough space for writing decimal values."
msgstr "Nicht genügend Platz zum Schreiben von Dezimalwerten."

#: heksedit.rc:1166
#, c-format
msgid "Number of times to paste must be at least 1."
msgstr "Die Anzahl der Einfügungen muss mindestens 1 betragen."

#: heksedit.rc:1167
#, c-format
msgid "Tried to paste zero-length array."
msgstr "Es wurde versucht, ein Array mit der Länge Null einzufügen."

#: heksedit.rc:1168
#, c-format
msgid "Not enough memory for inserting."
msgstr "Nicht genug Speicher zum Einfügen."

#: heksedit.rc:1169
#, c-format
msgid "Not enough space for overwriting."
msgstr "Nicht genug Speicher zum Einfügen."

#: heksedit.rc:1175
#, c-format
msgid "Can't fill a selection with a file of zero size."
msgstr "Eine Auswahl kann nicht mit einer Datei der Größe Null gefüllt werden."

#: heksedit.rc:1176
#, c-format
msgid "Can't fill a selection with a string of zero size."
msgstr "Eine Auswahl kann nicht mit einer Zeichenfolge der Größe Null gefüllt werden."

#: heksedit.rc:1177
#, c-format
msgid "Too many bytes to fill with or some other error."
msgstr "Zu viele Bytes zum Füllen oder ein anderer Fehler."

#: heksedit.rc:1183
#, c-format
msgid "You have chosen an offset but it is negative, which is invalid."
msgstr "Sie haben ein Offset gewählt, aber er ist negativ, was ungültig ist."

#: heksedit.rc:1184
#, c-format
msgid "The target value is invalid."
msgstr "Der Zielwert ist ungültig."

#: heksedit.rc:1185
#, c-format
msgid "Cannot move/copy a block of zero length."
msgstr "Kann einen Block der Länge Null nicht verschieben/kopieren."

#: heksedit.rc:1186
#, c-format
msgid "The chosen block extends into non-existent data."
msgstr "Der gewählte Block erstreckt sich auf nicht vorhandene Daten."

#: heksedit.rc:1187
#, c-format
msgid "The block was not moved!"
msgstr "Der Block wurde nicht verschoben!"

#: heksedit.rc:1188
#, c-format
msgid "Cannot move/copy the block outside the data."
msgstr "Der Block kann nicht außerhalb der Daten verschoben/kopiert werden."

#: heksedit.rc:1194
#, c-format
msgid "&Size of file: %lld bytes. Load how many bytes:"
msgstr "Größe der Datei: %lld Bytes. Wie viele Bytes laden:"

#: heksedit.rc:1195
#, c-format
msgid "Cannot open more than 2 GB of data."
msgstr "Es können nicht mehr als 2 GB an Daten geöffnet werden."

#: heksedit.rc:1196
#, c-format
msgid "Specified number of bytes to load greater than file size."
msgstr "Angegebene Anzahl der zu ladenden Bytes größer als die Dateigröße."

#: heksedit.rc:1197
#, c-format
msgid "Too many bytes to load."
msgstr "Zu viele Bytes zu laden."

#: heksedit.rc:1203
#, c-format
msgid "Cannot reverse single byte."
msgstr "Einzelbyte kann nicht umgekehrt werden."

#: heksedit.rc:1204
#, c-format
msgid "The chosen block extends into non-existent data. The offsets will be shifted to correct positions."
msgstr "Der gewählte Block erstreckt sich auf nicht vorhandene Daten. Die Offsets werden auf korrekte Positionen verschoben."

#: heksedit.rc:1210
#, c-format
msgid "Link names are \"Frhed.lnk\""
msgstr "Linknamen sind \"Frhed.lnk\""

#: heksedit.rc:1211
#, c-format
msgid "This folder already contains a file called \"Frhed.lnk\""
msgstr "Dieser Ordner enthält bereits eine Datei namens \"Frhed.lnk\""

#: heksedit.rc:1212
#, c-format
msgid "\"Frhed.lnk\" can be added to this folder."
msgstr "\"Frhed.lnk\" kann zu diesem Ordner hinzugefügt werden."

#: heksedit.rc:1213
#, c-format
msgid "\"Frhed.lnk\" cannot be added to this folder."
msgstr "\"Frhed.lnk\" kann diesem Ordner nicht hinzugefügt werden."

#: heksedit.rc:1214
#, c-format
msgid "Frhed can start searching here."
msgstr "Frhed kann hier mit der Suche beginnen."

#: heksedit.rc:1215
#, c-format
msgid "Frhed cannot start the search from here."
msgstr "Frhed kann die Suche nicht von hier aus starten."

#: heksedit.rc:1216
#, c-format
msgid "Could not save shortcut entries."
msgstr "Konnte Verknüpfungseinträge nicht speichern."

#: heksedit.rc:1217
#, c-format
msgid "Cannot move more than 1 link at a time."
msgstr "Kann nicht mehr als 1 Link auf einmal bewegen."

#: heksedit.rc:1218
#, c-format
msgid "No link selected to move."
msgstr "Kein Link zum Verschieben ausgewählt."

#: heksedit.rc:1219
#, c-format
msgid "Couldn't find the selected item."
msgstr "Konnte den ausgewählten Eintrag nicht finden."

#: heksedit.rc:1220
#, c-format
msgid "Place a link to Frhed in..."
msgstr "Einen Link zu Frhed setzen in..."

#: heksedit.rc:1221
#, c-format
msgid "Move the link to Frhed to..."
msgstr "Den Link zu Frhed verschieben nach..."

#: heksedit.rc:1222
#, c-format
msgid "There is already a link in that folder."
msgstr "Es gibt bereits einen Link in diesem Ordner."

#: heksedit.rc:1223
#, c-format
msgid "Existing links to old versions of Frhed will be updated to this version.\nAre you sure you want to continue?"
msgstr "Bestehende Links zu alten Versionen von Frhed werden auf diese Version aktualisiert.\nSind Sie sicher, dass Sie fortfahren möchten?"

#: heksedit.rc:1224
#, c-format
msgid "Pick a folder to start searching in."
msgstr "Wählen Sie einen Ordner aus, in dem die Suche beginnen soll."

#: heksedit.rc:1225
#, c-format
msgid "No links selected to delete."
msgstr "Keine Links zum Löschen ausgewählt."

#: heksedit.rc:1231
#, c-format
msgid "Selection too large."
msgstr "Auswahl zu groß."

#: heksedit.rc:1232
#, c-format
msgid "Could not find data."
msgstr "Konnte keine Daten finden."

#: heksedit.rc:1233
#, c-format
msgid "Findstring is zero bytes long."
msgstr "Suchzeichenfolge ist null Bytes lang."

#: heksedit.rc:1234
#, c-format
msgid "Could not find any more occurrences."
msgstr "Konnte keine weiteren Vorkommen finden."

#: heksedit.rc:1235
#, c-format
msgid "String to find not specified."
msgstr "Zu findende Zeichenfolge nicht angegeben."

#: heksedit.rc:1241
#, c-format
msgid "You need to have Administrator privileges for getting list of drives."
msgstr "Sie müssen Administratorrechte haben, um eine Liste von Laufwerken zu erhalten."

#: heksedit.rc:1242
#, c-format
msgid "Unable to open drive."
msgstr "Laufwerk kann nicht geöffnet werden."

#: heksedit.rc:1243
#, c-format
msgid "Cylinders"
msgstr "Zylinder"

#: heksedit.rc:1244
#, c-format
msgid "Sectors"
msgstr "Sektoren"

#: heksedit.rc:1245
#, c-format
msgid "Tracks per cylinder"
msgstr "Spuren pro Zylinder"

#: heksedit.rc:1246
#, c-format
msgid "Sectors per track"
msgstr "Sektoren pro Spur"

#: heksedit.rc:1247
#, c-format
msgid "Bytes per sector"
msgstr "Bytes pro Sektor"

#: heksedit.rc:1248
#, c-format
msgid "Total size in bytes"
msgstr "Gesamtgröße in Bytes"

#: heksedit.rc:1249
#, c-format
msgid "%s:Sector %I64d"
msgstr "%s:Sektor %I64d"

#: heksedit.rc:1250
#, c-format
msgid "Drive %d, Partition %d (%s)"
msgstr "Laufwerk %d, Partition %d (%s)"

#: heksedit.rc:1251
#, c-format
msgid "Drive %d (%s)"
msgstr "Laufwerk %d (%s)"

#: heksedit.rc:1257
#, c-format
msgid "Not enough memory to import data.\nCannot continue!\nDo you want to keep what has been found so far?"
msgstr "Nicht genug Speicher zum Importieren von Daten.\nKann nicht fortfahren!\nWollen Sie das bisher Gefundene behalten?"

#: heksedit.rc:1258
#, c-format
msgid "The first offset found was 0x%llx, which is greater than zero.\nDo you want to insert %lld null bytes at the start of the data?"
msgstr "Der erste gefundene Offset war 0x%llx, was größer als Null ist.\nWollen Sie %lld Nullbytes am Anfang der Daten einfügen?"

#: heksedit.rc:1259
#, c-format
msgid "Invalid offset found.\nIgnore further invalid offsets?"
msgstr "Ungültiger Offset gefunden.\nWeitere ungültige Offsets ignorieren?"

#: heksedit.rc:1260
#, c-format
msgid "Character data does not agree with hex data.\nIgnore further mismatched data?\nNB: Hex data will be used when ignoring."
msgstr "Zeichendaten stimmen nicht mit Hex-Daten überein.\nWeitere nicht übereinstimmende Daten ignorieren?\nÜbrigens werden Hex-Daten beim Ignorieren verwendet."

#: heksedit.rc:1261
#, c-format
msgid "Illegal character in hex data.\nCannot continue!\nDo you want to keep what has been found so far?"
msgstr "Illegale Zeichen in hexadezimalen Daten.\nKann nicht fortfahren!\nWollen Sie das bisher Gefundene behalten?"

#: heksedit.rc:1262
#, c-format
msgid "Unexpected end of data found.\nCannot continue!\nDo you want to keep what has been found so far?"
msgstr "Unerwartetes Ende der gefundenen Daten.\nKann nicht fortfahren!\nWollen Sie das bisher Gefundene behalten?"

#: heksedit.rc:1263
#, c-format
msgid "Does this data have the same format as the Frhed display?\nThis data contains "
msgstr "Haben diese Daten dasselbe Format wie die Frhed-Anzeige?\nDiese Daten enthalten "

#: heksedit.rc:1264
#, c-format
msgid "characters other than whitespace and hexdigits (like Frhed display)."
msgstr "andere Zeichen als Leerzeichen und Hexadezimalziffern (Wie Frhed-Anzeige)."

#: heksedit.rc:1265
#, c-format
msgid "only whitespace and hexdigits (unlike Frhed display)."
msgstr "nur Leerzeichen und Hexadezimalziffern (Im Gegensatz zur Frhed-Anzeige)."

#: heksedit.rc:1271
#, c-format
msgid "Data to replace must be selected."
msgstr "Zu ersetzende Daten müssen ausgewählt werden."

#: heksedit.rc:1272
#, c-format
msgid "Could not delete selected data."
msgstr "Konnte ausgewählte Daten nicht löschen."

#: heksedit.rc:1273
#, c-format
msgid "Replacing failed."
msgstr "Ersetzen fehlgeschlagen."

#: heksedit.rc:1274
#, c-format
msgid "Could not translate text to binary."
msgstr "Konnte Text nicht ins Binäre übersetzen."

#: heksedit.rc:1275
#, c-format
msgid "To-replace and replace-with data are same."
msgstr "Suchen- und Ersetzen-Daten sind gleich."

#: heksedit.rc:1276
#, c-format
msgid "%d occurrences replaced."
msgstr "%d Vorkommen ersetzt."

#: heksedit.rc:1277
#, c-format
msgid "Could not use selection as replace target."
msgstr "Konnte die Auswahl nicht als Ersatzziel verwenden."

#: heksedit.rc:1283
#, c-format
msgid "Selected: Offset %zd=0x%zx to %zd=0x%zx (%zd byte(s))"
msgstr "Ausgewählt: Offset %zd=0x%zx bis %d=0x%zx (%zd Byte(s))"

#: heksedit.rc:1284
#, c-format
msgid "Offset %zd=0x%zx"
msgstr "Offset %zd=0x%zx"

#: heksedit.rc:1285
#, c-format
msgid "Bits"
msgstr "Bits"

#: heksedit.rc:1286
#, c-format
msgid "Unsigned"
msgstr "Ohne Vorzeichen"

#: heksedit.rc:1288
#, c-format
msgid "END"
msgstr "Ende"

#: heksedit.rc:1289
#, c-format
msgid "W"
msgstr "W"

#: heksedit.rc:1291
#, c-format
msgid "Signed"
msgstr "Mit Vorzeichen"

#: heksedit.rc:1292
#, c-format
msgid "Size"
msgstr "Größe"

#: heksedit.rc:1299
#, c-format
msgid "Template file:"
msgstr "Vorlagendatei:"

#: heksedit.rc:1300
#, c-format
msgid "Applied at offset:"
msgstr "Am Offset angewendet:"

#: heksedit.rc:1301
#, c-format
msgid "= %d (signed) = %u (unsigned) = 0x%x = '%c'"
msgstr "= %d (mit Vorzeichen) = %u (ohne Vorzeichen) = 0x%x = '%c'"

#: heksedit.rc:1302
#: heksedit.rc:1305
#: heksedit.rc:1307
#, c-format
msgid "= %d (signed) = %u (unsigned) = 0x%x"
msgstr "= %d (mit Vorzeichen) = %u (ohne Vorzeichen) = 0x%x"

#: heksedit.rc:1303
#, c-format
msgid "ERROR: not enough space for byte-size data."
msgstr "FEHLER: Nicht genug Platz für Daten in Byte-Größe."

#: heksedit.rc:1304
#, c-format
msgid "ERROR: missing variable name."
msgstr "FEHLER: Fehlender Variablenname"

#: heksedit.rc:1306
#, c-format
msgid "ERROR: not enough space for WORD-size data."
msgstr "FEHLER: Nicht genügend Platz für Daten in WORD-Größe."

#: heksedit.rc:1308
#, c-format
msgid "ERROR: not enough space for DWORD-size data."
msgstr "FEHLER: Nicht genügend Platz für Daten in DWORD-Größe."

#: heksedit.rc:1309
#, c-format
msgid "= %f = 0x%x"
msgstr "= %f = 0x%x"

#: heksedit.rc:1310
#, c-format
msgid "ERROR: not enough space for float-size data."
msgstr "FEHLER: Nicht genügend Platz für Daten in Gleitkomma-Größe."

#: heksedit.rc:1311
#, c-format
msgid "= %g"
msgstr "= %g"

#: heksedit.rc:1312
#, c-format
msgid "ERROR: not enough space for double-size data."
msgstr "FEHLER: Nicht genug Platz für Daten in doppelter Größe."

#: heksedit.rc:1313
#, c-format
msgid "ERROR: Unknown variable type:"
msgstr "FEHLER: Unbekannter Variablentyp:"

#: heksedit.rc:1319
#, c-format
msgid "-> Length of template = %zu bytes."
msgstr "-> Länge der Vorlage = %zu Bytes."

#: heksedit.rc:1320
#, c-format
msgid "Template files (*.tpl)\0*.tpl\0\0"
msgstr "Vorlagendateien (*.tpl)\0*.tpl\0\0"

#: heksedit.rc:1321
#, c-format
msgid "Choose template file"
msgstr "Vorlagendatei auswählen"

#: heksedit.rc:1322
#, c-format
msgid "Could not open template file %s."
msgstr "Konnte die Vorlagendatei %s nicht öffnen."

#: heksedit.rc:1323
#, c-format
msgid "Could not load template from file %s."
msgstr "Die Vorlage konnte nicht aus der Datei %s geladen werden."

#: heksedit.rc:1329
#, c-format
msgid "Select an item to move."
msgstr "Ein Element zum Verschieben auswählen."

#: heksedit.rc:1333
#, c-format
msgid "No data to insert"
msgstr "Keine Daten zum Einfügen"

#: heksedit.rc:1334
#, c-format
msgid "TYMED_ISTORAGE is not supported for drag-drop."
msgstr "TYMED_ISTORAGE wird für Drag-and-Drop nicht unterstützt."

#: heksedit.rc:1340
#, c-format
msgid "Float size value:\n%g\n"
msgstr "Gleitkommawert:\n%g\n"

#: heksedit.rc:1341
#, c-format
msgid "Not enough space for float size value.\n"
msgstr "Nicht genug Platz für den Gleitkommawert.\n"

#: heksedit.rc:1342
#, c-format
msgid "\nDouble size value:\n%g\n"
msgstr "\nDoppelwortwert:\n%g\n"

#: heksedit.rc:1343
#, c-format
msgid "\nNot enough space for double size value.\n"
msgstr "\nNicht genug Platz für den Doppelwortwert.\n"

#: heksedit.rc:1344
#, c-format
msgid "File name and path: "
msgstr "Dateiname und Pfad: "

#: heksedit.rc:1345
#, c-format
msgid "\nPartially opened at offset 0x%zx = %zd.\nNumber of bytes read: %zd = %zd kilobytes.\n"
msgstr "\nTeilweise geöffnet bei Offset 0x%zx = %zd.\nAnzahl der gelesenen Bytes: %zd = %zd Kilobytes.\n"

#: heksedit.rc:1346
#, c-format
msgid "\nFile size: %zu bytes = %zu kilobytes.\n"
msgstr "\nDateigröße: %zu Bytes = %zu Kilobytes.\n"

#: heksedit.rc:1347
#, c-format
msgid "\nNumber of hexdump lines: %zu.\n"
msgstr "\nAnzahl der Hexdump-Zeilen: %zu.\n"

#: heksedit.rc:1348
#, c-format
msgid "This file could not be accessed and will be removed from the MRU list."
msgstr "Auf diese Datei konnte nicht zugegriffen werden und sie wird von der MRU-Liste gestrichen."

#: heksedit.rc:1349
#, c-format
msgid "Really reset colors to default values?"
msgstr "Farben wirklich auf Standardwerte zurücksetzen?"

#: heksedit.rc:1350
#, c-format
msgid "Are you sure you want to delete this file?"
msgstr "Sind Sie sicher, dass Sie diese Datei löschen möchten?"

#: heksedit.rc:1351
#, c-format
msgid "Could not delete file."
msgstr "Konnte die Datei nicht löschen."

#: heksedit.rc:1352
#, c-format
msgid "Really adopt the operating system colour scheme?"
msgstr "Das Farbschema des Betriebssystems wirklich übernehmen?"

#: heksedit.rc:1358
#, c-format
msgid "Do you want to save your changes?"
msgstr "Möchten Sie Ihre Änderungen speichern?"

#: heksedit.rc:1364
#, c-format
msgid "Hexdump saved."
msgstr "Hexdump gespeichert."

#: heksedit.rc:1365
#, c-format
msgid "Could not save Hexdump."
msgstr "Konnte Hexdump nicht speichern."

#: heksedit.rc:1366
#, c-format
msgid "Not enough memory for copying hexdump to clipboard."
msgstr "Nicht genügend Speicher für das Kopieren von Hexdump in die Zwischenablage."

#: heksedit.rc:1367
#, c-format
msgid "Could not copy hexdump to clipboard."
msgstr "Hexdump konnte nicht in die Zwischenablage kopiert werden."

#: heksedit.rc:1368
#, c-format
msgid "Hex Dump files(*.txt,*.hex)|*.txt;*.hex|All Files (*.*)|*.*|"
msgstr "Hexdump-Dateien(*.txt,*.hex)|*.txt;*.hex|All Files (*.*)|*.*|"

