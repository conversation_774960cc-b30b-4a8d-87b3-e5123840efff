﻿WINMERGE

WinMerge ist ein Open-Source-Tool zum Vergleichen und Zusammenführen für
Windows. WinMerge kann sowohl Ordner als auch Dateien vergleichen und
Unterschiede in einem visuellen Textformat darstellen, das leicht zu verstehen
und zu handhaben ist. WinMerge kann als externes Unterscheidungs- und
Zusammenführungswerkzeug oder als eigenständige Anwendung genutzt werden.

WinMerge hat viele hilfreiche unterstützende Funktionen, um das Vergleichen,
Synchronisieren und Zusammenführen so einfach wie möglich zu machen.
Für mehrere Programmiersprachen und andere Dateiformate gibt es eine
Syntaxhervorhebung.

Die neueste WinMerge-Version und WinMerge-Informationen sind verfügbar unter
https://winmerge.org/.

Schnellstart
============
Um zu erfahren, wie Sie grundlegende Operationen nach der Installation von
WinMerge ausführen können, klicken Sie Hilfe > WinMerge-Hilfe und navigieren
zum Thema "Quick start". Oder Sie gehen zur Webversion unter
https://manual.winmerge.org/en/Quick_start.html.

WinMerge-Hilfe
==============
Als WinMerge-Hilfe wird lokal die Microsoft-HTML-Datei WinMerge.chm
installiert. Um die Hilfe zu öffnen, klicken Sie auf Hilfe > WinMerge-Hilfe oder
drücken F1. Auf der Befehlszeile starten Sie die ausführbare WinMerge-Datei
mit dem Hilfe-Schalter /?.

Sie können auch die HTML-Version der WinMerge-Hilfe durchsuchen unter
https://manual.winmerge.org/.

WinMerge-Unterstützung
======================
Fragen oder Vorschläge zu WinMerge? Ein guter Ort, um zu beginnen, ist das
WinMerge-Community-Board unter https://forums.winmerge.org/. Entwickler
lesen häufig und antworten auf Fragen in beiden Foren. Nutzen Sie das offene
Diskussionsforum für allgemeine WinMerge-Probleme, wie Fragen zur Benutzung.
Verwenden Sie das Entwicklerforum für WinMerge-Entwicklungsprobleme.

Fehler und Funktionswünsche
===========================
Wenn ein Problem in den WinMerge-Foren nicht gelöst wird, gehen Sie zu
https://project.winmerge.org/ und klicken im Menü auf einen Link wie "Bugs"
oder "Feature Requests", wo Sie Beiträge durchsuchen oder einreichen können.

Wenn Sie einen Fehler einreichen, geben Sie bitte die WinMerge-Versionsnummer
in Ihrem Bericht an. Sie können ein Konfigurationsprotokoll erstellen, indem
Sie auf Hilfe > Konfiguration klicken. Bitte hängen Sie das Protokoll an den
Fehlerbericht an. Es enthält viele nützliche Informationen für Entwickler.


- Die WinMerge-Entwickler
