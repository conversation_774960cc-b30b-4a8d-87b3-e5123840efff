# This file is part from WinMerge <https://winmerge.org/>
# Released under the "GNU General Public License"
#
# Translators:
# * <PERSON><PERSON> (aka <PERSON>ichi<PERSON>) <tichij AT mail DOT com>
#
msgid ""
msgstr ""
"Project-Id-Version: WinMerge Shell Extension\n"
"Report-Msgid-Bugs-To: https://bugs.winmerge.org/\n"
"POT-Creation-Date: 2023-02-19 00:07+0000\n"
"PO-Revision-Date: 2023-02-20 07:29+0200\n"
"Last-Translator: Tichij <<EMAIL>>\n"
"Language-Team: Lithuanian <<EMAIL>>\n"
"Language: lt\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-Basepath: ../../ShellExtension/Languages\n"
"X-Generator: Poedit 3.2.2\n"

#. AFX_TARG_*
#: ShellExtension.rc:19
#, c-format
msgid "AFX_TARG_ENU"
msgstr "AFX_TARG_LIT"

#. LANGUAGE, SUBLANGUAGE
#: ShellExtension.rc:20
#, c-format
msgid "LANG_ENGLISH, SUBLANG_ENGLISH_US"
msgstr "LANG_LITHUANIAN, SUBLANG_DEFAULT"

#: ShellExtension.rc:111
#, c-format
msgid "ShellExtension"
msgstr "ShellExtension"

#: ShellExtension.rc:112
#, c-format
msgid "Win&Merge"
msgstr "Win&Merge"

#: ShellExtension.rc:113
#, c-format
msgid "Compare &As"
msgstr "Lyginti k&aip"

#: ShellExtension.rc:114
#, c-format
msgid "&Compare"
msgstr "&Lyginti"

#: ShellExtension.rc:115
#, c-format
msgid "Compare&..."
msgstr "Lyginti&..."

#: ShellExtension.rc:116
#, c-format
msgid "Select &Left"
msgstr "Parinkti &Kairįjį"

#: ShellExtension.rc:117
#, c-format
msgid "Select &Middle"
msgstr "Parinkti &Vidurinį"

#: ShellExtension.rc:118
#, c-format
msgid "Re-select &Left"
msgstr "Iš naujo parinkti &Kairįjį"
