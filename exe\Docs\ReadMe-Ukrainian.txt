﻿WINMERGE

WinMerge є вільною (Open Source) програмою для порівняння та об'єднання, яка
працює на усіх сучасних версіях Windows. Деякі функції потребують встановлення
додаткових компонентів.

Остання версія WinMerge та інша інформація про WinMerge доступна за адресою
https://winmerge.org

Початок роботи з WinMerge:
Щодо цього читайте відповідний розділ онлайн-довідки WinMerge:
https://manual.winmerge.org/Quick_start.html

HTML-довідка:
Довідка доступна онлайн
https://manual.winmerge.org/
її також можна встановити (якщо обрано) локально та завантажити окремо з
https://winmerge.org/ (див. документацію)

Підтримка скриптів:
Якщо бажаєте працювати зі скриптами, то для цього необхідно мати встановлений
Windows Scripting Host. Якщо виникають якісь помилки, що стосуються Ваших
скриптів, тоді відвідайте
https://msdn.microsoft.com/library/default.asp?url=/downloads/list/webdev.asp
щоб переконатися, що Ваш Scripting Host є останньої версії і неушкоджений.

Підтримка:
Розробники відповідають на запитання у форумі WinMerge на Sourceforge.net:
https://sourceforge.net/forum/?group_id=13216

Баґи та прохання про функції:
Повідомлення про баґи та прохання про нові фунції можна залишати у відстежувачах
RFE та баґів на sourceforge.net.

Відстежувач баґів:
https://sourceforge.net/tracker/?group_id=13216&atid=113216
При сповіщенні про баґи обов'язково вказуйте свою версію WinMerge!
WinMerge версії 2.2.0 та новішої може виводити "Журнал конфіґурації" з меню
Довідка -> Конфіґурація. Долучіть цю інформацію (як файл-долучення) до 
сповіщення про баґ, бо він містить багато корисної для розробників інформації.

Відстежувач RFE (запит нової фунції):
https://sourceforge.net/tracker/?group_id=13216&atid=363216


- Розробники WinMerge
