﻿WINMERGE

WinMerge Windows için Açık Kaynaklı bir karşılaştırma ve birleştirme aracıdır. WinMerge 
klasörleri ve dosyaları karşılaştırabilir, farkları görsel metin biç<PERSON>e, kolayca anlaşılıp
işlenebilecek şekilde sunar. WinMerge bir dış fark/birleştirme aracı olarak kullanılabileceği
gibi ayrı bir uygulama olarak da kullanılabilir.

WinMerge karşılaştırma, eşleştirme ve birleştirme işlemlerini olabildiğince kolay ve kullanışlı
şekilde yapabilmek için pek çok özelliğe sahiptir. Çeşitli programlama dilleri ve bazı dosya türlerini
tanıyarak buna göre yazım vurgulaması yapabilir.

Uygulamanın son sürümüne ve gerekli bilgilere 
https://winmerge.org/ adresinden ulaşabilirsiniz.

Hızlı Başlangıç
===============
WinMerge uygulamasını kurduktan sonra temel işlemleri nasıl yapabileceğinizi öğrenmek için
Yardım>WinMerge seçeneğine tıklayın ve hızlı başlangıç (quick start) başlığına gidin.
Aynı bilginin Web sürümüne ulaşmak için https://manual.winmerge.org/Quick_start.html adresine bakabilirsiniz.

WinMerge Yardımı
================
WinMerge Yardımı, kurulum sırasında yerel diske kopyalanan bir Microsoft HTML 
yardım dosyasıdır (WinMerge.chm). Yardım almak
için, Yardım>WinMerge seçeneğine tıklayın veya WinMerge ekranındayken F1 tuşuna basın. Komut
satırında ise WinMerge uygulamasını /? yardım anahtarı ile çalıştırın.

WinMerge yardımının HTML sürümüne 
https://manual.winmerge.org/ adresinden de ulaşabilirsiniz.

WinMerge Desteği
================
WinMerge hakkında sorularınız ve önerileriniz mi var? Bunun için https://forums.winmerge.org/
adresinden ulaşabileceğiniz WinMerge topluluğu forumunu kullanabilirsiniz. Yazılım geliştiriciler
forumlardaki soruları sıklıkla okuyup yanıtlıyorlar. Kullanım gibi genel WinMerge
konuları için Açık Tartışma (Open Discussion) forumunu, yazılım geliştirme ile ilgili konular
için de Geliştirici (Developers) forumunu kullanabilirsiniz.

Hatalar ve özellik istekleri
===============================
WinMerge forumlarında çözümü olmayan bir hata ile karşılaşırsanız, https://project.winmerge.org/
adresine giderek proje izleyicilere bakın. İzleyiciler menüsünden hatalar (bugs) veya özellik
istekleri (feature requests) gibi bir bağlantıya tıklayarak başlıklara göz atıp yeni bir başlık açabilirsiniz.

Bir hata bildiriyorsanız, WinMerge sürümünü de belirtin. 
Yardım>Yapılandırma seçeneğine tıklayarak bir yapılandırma günlüğü oluşturabilirsiniz.
Bu günlüğü hata raporunuza ekleyin. Günlükte geliştiriciler için yaralrı olabilecek pek çok 
bilgi bulunur.


- WinMerge Geliştiricileri
