# This file is part from WinMerge <https://winmerge.org/>
# Released under the "GNU General Public License"
#
msgid ""
msgstr ""
"Project-Id-Version: WinMerge Shell Extension\n"
"Report-Msgid-Bugs-To: https://bugs.winmerge.org/\n"
"POT-Creation-Date: 2011-10-11 01:08+0000\n"
"PO-Revision-Date: \n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: ChineseSimplified <<EMAIL>>\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-Basepath: ../../ShellExtension/Languages\n"
"X-Generator: Poedit 3.2.2\n"

#. AFX_TARG_*
#: ShellExtension.rc:19
#, c-format
msgid "AFX_TARG_ENU"
msgstr "AFX_TARG_CHS"

#. LANGUAGE, SUBLANGUAGE
#: ShellExtension.rc:20
#, c-format
msgid "LANG_ENGLISH, SUBLANG_ENGLISH_US"
msgstr "LANG_CHINESE, SUBLANG_CHINESE_SIMPLIFIED"

#: ShellExtension.rc:111
#, c-format
msgid "ShellExtension"
msgstr "ShellExtension"

#: ShellExtension.rc:112
#, c-format
msgid "Win&Merge"
msgstr "Win&Merge"

#: ShellExtension.rc:113
#, c-format
msgid "Compare &As"
msgstr "比较方式(&A)"

#: ShellExtension.rc:114
#, c-format
msgid "&Compare"
msgstr "比较(&C)"

#: ShellExtension.rc:115
#, c-format
msgid "Compare&..."
msgstr "比较(&.)..."

#: ShellExtension.rc:116
#, c-format
msgid "Select &Left"
msgstr "选择左侧(&L)"

#: ShellExtension.rc:117
#, c-format
msgid "Select &Middle"
msgstr "选择中间(&M)"

#: ShellExtension.rc:118
#, c-format
msgid "Re-select &Left"
msgstr "重选择左侧(&L)"
