﻿;!@Lang2@!UTF-8!
; 23.01 : 2023-05-15 : <PERSON>
;
;
;
;
;
;
;
;
;
;
0
7-Zip
Russian
Русский
401
OK
Отмена



&Да
&Нет
&Закрыть
Помощь

&Продолжить
440
Да для &всех
Нет для в&сех
Стоп
Перезапуск
&Фоном
&На передний план
&Пауза
На паузе
Вы действительно хотите прервать операцию?
500
&Файл
&Правка
&Вид
&Избранное
С&ервис
&Справка
540
&Открыть
Открыть &внутри
Открыть снару&жи
Просмотр
&Редактировать
Переи&меновать
&Копировать в...
&Переместить в...
&Удалить
Ра&збить файл...
О&бъединить файлы...
Сво&йства
Комме&нтарий...
Контрольная сумма
Сравнить
&Создать Папку
Созд&ать Файл
В&ыход
Ссылка
&Альтернативные Потоки
600
Выделить в&се
Убрать выделение
&Обратить в&ыделение
Выделить...
Убрать выделение...
Выделить по типу
Убрать выделение по типу
700
&Крупные значки
&Мелкие значки
Спис&ок
&Таблица
730
Без сортировки
Плоский режим
&2 Панели
&Панели инструментов
Открыть корневую папку
Переход на один уровень вверх
История папок...
О&бновить
Автообновление
750
Панель кнопок архиватора
Стандартная панель кнопок
Большие кнопки
Надписи на кнопках
800
Добавить папку в &избранное как
Закладка
900
Настройки...
Тестирование производительности
960
&Оглавление...
О &программе...
1003
Путь
Имя
Расширение
Папка
Размер
Сжатый
Атрибуты
Создан
Открыт
Изменен
Непрерывный
Комментарий
Зашифрован
Разбит До
Разбит После
Словарь

Тип
Анти
Метод
Система
Файловая Система
Пользователь
Группа
Блок
Комментарий
Позиция
Путь
Папок
Файлов
Версия
Том
Многотомный
Смещение
Ссылок
Блоков
Томов

64-битный
Big-endian
Процессор
Физический Размер
Размер Заголовков
Контрольная Сумма
Характеристики
Виртуальный Адрес
ID
Короткое Имя
Создатель
Размер Сектора
Режим
Символьная Ссылка
Ошибка
Емкость
Свободно
Размер кластера
Метка
Локальное имя
Провайдер
NT Безопасность
Альтернативный Поток
Вспомогательный
Удаленный
Дерево


Тип Ошибки
Ошибки
Ошибки
Предупреждения
Предупреждение
Потоки
Альтернативные Потоки
Размер Альтернативных потоков
Виртуальный Размер
Распакованный Размер
Общий Физический Размер
Номер Тома
Подтип
Короткий Комментарий
Кодовая Страница



Размер Остатка
Размер Встроенного Блока
Ссылка
Жесткая Ссылка
iNode

Только для чтения

Ссылка копирования


Изменены метаданные
2100
Настройки
Язык
Язык:
Редактор
&Редактор:
&Программа сравнения:
2200
Система
Ассоциировать 7-Zip с файлами:
Все пользователи
2301
Встроить 7-Zip в контекстное меню оболочки
Каскадное контекстное меню
Элементы контекстного меню:
Иконки в контекстном меню
2320
<Папка>
<Архив>
Открыть архив
Распаковать
Добавить к архиву...
Тестировать
Распаковать здесь
Распаковать в {0}
Добавить к {0}
Сжать и отправить по email...
Сжать в {0} и отправить по email
2400
Папки
&Рабочая папка
&Системная временная папка
&Текущая
&Задать:
Использовать только для сменных носителей
Укажите положение для временных архивов.
2500
Настройки
Показывать элемент ".."
Показывать реальные иконки файлов
Показывать системное меню
Курсор на всю строку
Показывать разделители
Открывать одним щелчком
Альтернативный режим пометки
Использовать большие страницы памяти
2900
О программе 7-Zip
7-Zip является свободно распространяемой программой.
3000
Недостаточно свободной памяти
Ошибок не найдено
Выделено объектов: {0}
Не удалось создать папку '{0}'
Операции изменения не поддерживаются для этого архива.
Не удалось открыть файл '{0}' как архив
Не удалось открыть зашифрованный архив '{0}'. Неверный пароль?
Неподдерживаемый тип архива
Файл {0} уже существует
Файл '{0}' был изменен.\nВы хотите обновить его в архиве?
Не удалось обновить файл\n'{0}'
Не удалось запустить редактор
Файл похож на вирус (имя файла содержит длинную последовательность пробелов).
Операция не может быть исполнена из папки, которая имеет длинный путь.
Вы должны выделить один файл
Вы должны выделить один или несколько файлов
Слишком много элементов
Не удалось открыть файл как {0} архив
Файл открыт как {0} архив
Архив открыт со смещением
3300
Распаковка
Упаковка
Тестирование
Открытие...
Сканирование...
Удаление
3320
Добавление
Обновление
Анализ
Копирование
Перепаковка
Пропуск
Удаление
Создание заголовков
3400
Извлечь
&Распаковать в:
Укажите положение для извлекаемых файлов.
3410
Пути к файлам:
Полные пути
Без путей
Абсолютные пути
Относительные пути
3420
Перезапись:
С подтверждением
Без подтверждения
Пропускать
Переименовать автоматически
Переименовать существующие
3430
Устранить дублирование корневой папки
Устанавливать права доступа
3440
Распространять поток Zone.Id:
Для файлов Office
3500
Подтверждение замены файла
Папка уже содержит обрабатываемый файл.
Заменить существующий файл
следующим файлом?
{0} байтов
Переименовать автом.
3700
Неподдерживаемый метод сжатия для файла '{0}'.
Ошибка в данных в '{0}'. Файл испорчен.
Ошибка CRC в '{0}'. Файл испорчен.
Ошибка в данных зашифрованного файла '{0}'. Неверный пароль?
Ошибка CRC для зашифрованного файла '{0}'. Неверный пароль?
3710
Неверный пароль?
3721
Неподдерживаемый метод сжатия
Ошибка в данных
Ошибка CRC
Недоступные данные
Неожиданный конец данных
Есть данные после конца блока полезных данных
Не является архивом
Ошибка в заголовках
Неверный пароль
3763
Недоступно начало архива
Неподтвержденное начало архива



Неподдерживаемая функциональность
3800
Ввод пароля
&Введите пароль:
Повторите пароль:
&Показать пароль
Пароли не совпадают
Для пароля используйте только символы латинского алфавита, цифры и специальные символы (!, #, $, ...)
Пароль слишком длинный
&Пароль
3900
Прошло:
Осталось:
Всего:
Скорость:
Размер:
Степень сжатия:
Ошибок:
Архивов:
4000
Добавить к архиву
&Архив:
&Режим изменения:
&Формат архива:
&Уровень сжатия:
&Метод сжатия:
Размер &словаря:
Размер с&лова:
Размер блока:
Число потоков:
&Параметры:
&Опции
Создать SF&X-архив
Сжимать открытые для записи файлы
Шифрование
Метод шифрования:
&Шифровать имена файлов
Объем памяти для упаковки:
Объем памяти для распаковки:
Удалять файлы после сжатия
4040
Сохранять символьные ссылки
Сохранять жесткие ссылки
Сохранять альтернативные потоки
Сохранять права доступа
4050
Без сжатия
Скоростной
Быстрый
Нормальный
Максимальный
Ультра
4060
Добавить и заменить
Обновить и добавить
Обновить
Синхронизировать
4070
Пролистать
Все файлы
По размеру файла
Непрерывный
4080
Время
Точность отметок времени:
Сохранять время изменения файлов
Сохранять время создания файлов
Сохранять время последнего доступа к файлам
Уcтановить время архива по времени самого нового файла
Не изменять время доступа у исходных файлов
4090
сек
нс
6000
Копировать
Переместить
Копировать в:
Переместить в:
Копирование...
Перемещение...
Переименование...
Укажите папку.
Операция не поддерживается для этой папки.
Ошибка при переименовании файла или папки
Подтверждение копирования файлов
Вы действительно хотите скопировать эти файлы в архив
6100
Подтверждение удаления файла
Подтверждение удаления папки
Подтверждение удаления группы файлов
Вы действительно хотите удалить "{0}"?
Вы действительно хотите удалить папку "{0}" и все ее содержимое?
Вы действительно хотите удалить эти объекты ({0} шт.)?
Удаление...
Ошибка при удалении файла или папки
Система не поддерживает операцию удаления файлов с длинными путями в корзину
6300
Создать папку
Создать файл
Имя папки:
Имя файла:
Новая папка
Новый файл
Ошибка при создании папки
Ошибка при создании файла
6400
Комментарий
&Комментарий:
Выделить
Убрать выделение
Маска:
6600
Свойства
История папок
Сообщения
Сообщение
7100
Компьютер
Сеть
Документы
Система
7200
Добавить
Извлечь
Тестировать
Копировать
Переместить
Удалить
Информация
7300
Разбить файл
&Разбить в:
Разбить на &тома размером (в байтах):
Разбиение...
Подтверждение разбиения
Вы действительно хотите разбить файл на {0} частей?
Размер тома должен быть меньше размера исходного файла
Ошибка в поле для задания размера томов
Установленный размер тома: {0} байтов.\nВы действительно хотите разбить архив на такие тома?
7400
Объединить файлы
&Объединить в:
Объединение...
Необходимо выделить только первую часть разбитого файла
Не удалось распознать разбитый файл
Не удалось найти более одной части разбитого файла
7500
Вычисление контрольной суммы...
Контрольная сумма
Сумма CRC для данных:
Сумма CRC для данных и имен:
7600
Тестирование производительности
Объем памяти:
Упаковка
Распаковка
Рейтинг
Общий рейтинг
Текущий
Итоговый
Нагрузка
Рейтинг / Нагр.
Проходов:
7700
Ссылка
Связать
Источник:
Цель:
7710
Тип ссылки
Жесткая ссылка
Символьная ссылка (Файл)
Символьная ссылка (Папка)
Точка соединения (Junction)
