<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang xml:lang>
<head>
  <meta charset="utf-8" />
  <meta name="generator" content="pandoc" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
  <title>ChangeLog</title>
  <style>
    code{white-space: pre-wrap;}
    span.smallcaps{font-variant: small-caps;}
    span.underline{text-decoration: underline;}
    div.column{display: inline-block; vertical-align: top; width: 50%;}
    div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}
    ul.task-list{list-style: none;}
    .display.math{display: block; text-align: center; margin: 0.5rem auto;}
  </style>
  <link rel="stylesheet" href="data:text/css,article%2Caside%2Cdetails%2Cfigcaption%2Cfigure%2Cfooter%2Cheader%2Chgroup%2Cmain%2Cnav%2Csection%2Csummary%20%7Bdisplay%3A%20block%3B%7Daudio%2Ccanvas%2Cvideo%20%7Bdisplay%3A%20inline%2Dblock%3B%7Daudio%3Anot%28%5Bcontrols%5D%29%20%7Bdisplay%3A%20none%3Bheight%3A%200%3B%7D%5Bhidden%5D%2Ctemplate%20%7Bdisplay%3A%20none%3B%7Dhtml%20%7Bfont%2Dfamily%3A%20sans%2Dserif%3B%20%2Dms%2Dtext%2Dsize%2Dadjust%3A%20100%25%3B%20%2Dwebkit%2Dtext%2Dsize%2Dadjust%3A%20100%25%3B%20%7Dbody%20%7Bmargin%3A%200%3B%7Da%20%7Bbackground%3A%20transparent%3B%7Da%3Afocus%20%7Boutline%3A%20thin%20dotted%3B%7Da%3Aactive%2Ca%3Ahover%20%7Boutline%3A%200%3B%7Dh1%20%7Bfont%2Dsize%3A%202em%3Bmargin%3A%200%2E67em%200%3B%7Dabbr%5Btitle%5D%20%7Bborder%2Dbottom%3A%201px%20dotted%3B%7Db%2Cstrong%20%7Bfont%2Dweight%3A%20bold%3B%7Ddfn%20%7Bfont%2Dstyle%3A%20italic%3B%7Dhr%20%7B%2Dmoz%2Dbox%2Dsizing%3A%20content%2Dbox%3Bbox%2Dsizing%3A%20content%2Dbox%3Bheight%3A%200%3B%7Dmark%20%7Bbackground%3A%20%23ff0%3Bcolor%3A%20%23000%3B%7Dcode%2Ckbd%2Cpre%2Csamp%20%7Bfont%2Dfamily%3A%20monospace%2C%20serif%3Bfont%2Dsize%3A%201em%3B%7Dpre%20%7Bwhite%2Dspace%3A%20pre%2Dwrap%3B%7Dq%20%7Bquotes%3A%20%22%5C201C%22%20%22%5C201D%22%20%22%5C2018%22%20%22%5C2019%22%3B%7Dsmall%20%7Bfont%2Dsize%3A%2080%25%3B%7Dsub%2Csup%20%7Bfont%2Dsize%3A%2075%25%3Bline%2Dheight%3A%200%3Bposition%3A%20relative%3Bvertical%2Dalign%3A%20baseline%3B%7Dsup%20%7Btop%3A%20%2D0%2E5em%3B%7Dsub%20%7Bbottom%3A%20%2D0%2E25em%3B%7Dimg%20%7Bborder%3A%200%3B%7Dsvg%3Anot%28%3Aroot%29%20%7Boverflow%3A%20hidden%3B%7Dfigure%20%7Bmargin%3A%200%3B%7Dfieldset%20%7Bborder%3A%201px%20solid%20%23c0c0c0%3Bmargin%3A%200%202px%3Bpadding%3A%200%2E35em%200%2E625em%200%2E75em%3B%7Dlegend%20%7Bborder%3A%200%3B%20padding%3A%200%3B%20%7Dbutton%2Cinput%2Cselect%2Ctextarea%20%7Bfont%2Dfamily%3A%20inherit%3B%20font%2Dsize%3A%20100%25%3B%20margin%3A%200%3B%20%7Dbutton%2Cinput%20%7Bline%2Dheight%3A%20normal%3B%7Dbutton%2Cselect%20%7Btext%2Dtransform%3A%20none%3B%7Dbutton%2Chtml%20input%5Btype%3D%22button%22%5D%2C%20input%5Btype%3D%22reset%22%5D%2Cinput%5Btype%3D%22submit%22%5D%20%7B%2Dwebkit%2Dappearance%3A%20button%3B%20cursor%3A%20pointer%3B%20%7Dbutton%5Bdisabled%5D%2Chtml%20input%5Bdisabled%5D%20%7Bcursor%3A%20default%3B%7Dinput%5Btype%3D%22checkbox%22%5D%2Cinput%5Btype%3D%22radio%22%5D%20%7Bbox%2Dsizing%3A%20border%2Dbox%3B%20padding%3A%200%3B%20%7Dinput%5Btype%3D%22search%22%5D%20%7B%2Dwebkit%2Dappearance%3A%20textfield%3B%20%2Dmoz%2Dbox%2Dsizing%3A%20content%2Dbox%3B%2Dwebkit%2Dbox%2Dsizing%3A%20content%2Dbox%3B%20box%2Dsizing%3A%20content%2Dbox%3B%7Dinput%5Btype%3D%22search%22%5D%3A%3A%2Dwebkit%2Dsearch%2Dcancel%2Dbutton%2Cinput%5Btype%3D%22search%22%5D%3A%3A%2Dwebkit%2Dsearch%2Ddecoration%20%7B%2Dwebkit%2Dappearance%3A%20none%3B%7Dbutton%3A%3A%2Dmoz%2Dfocus%2Dinner%2Cinput%3A%3A%2Dmoz%2Dfocus%2Dinner%20%7Bborder%3A%200%3Bpadding%3A%200%3B%7Dtextarea%20%7Boverflow%3A%20auto%3B%20vertical%2Dalign%3A%20top%3B%20%7Dtable%20%7Bborder%2Dcollapse%3A%20collapse%3Bborder%2Dspacing%3A%200%3B%7D%2Ego%2Dtop%20%7Bposition%3A%20fixed%3Bbottom%3A%202em%3Bright%3A%202em%3Btext%2Ddecoration%3A%20none%3Bbackground%2Dcolor%3A%20%23E0E0E0%3Bfont%2Dsize%3A%2012px%3Bpadding%3A%201em%3Bdisplay%3A%20inline%3B%7Dhtml%2Cbody%7B%20margin%3A%20auto%3Bpadding%2Dright%3A%201em%3Bpadding%2Dleft%3A%201em%3Bmax%2Dwidth%3A%2044em%3B%20color%3Ablack%3B%7D%2A%3Anot%28%27%23mkdbuttons%27%29%7Bmargin%3A0%3Bpadding%3A0%7Dbody%7Bfont%3A13%2E34px%20helvetica%2Carial%2Cfreesans%2Cclean%2Csans%2Dserif%3B%2Dwebkit%2Dfont%2Dsmoothing%3Asubpixel%2Dantialiased%3Bline%2Dheight%3A1%2E4%3Bpadding%3A3px%3Bbackground%3A%23fff%3Bborder%2Dradius%3A3px%3B%2Dmoz%2Dborder%2Dradius%3A3px%3B%2Dwebkit%2Dborder%2Dradius%3A3px%7Dp%7Bmargin%3A1em%200%7Da%7Bcolor%3A%234183c4%3Btext%2Ddecoration%3Anone%7Dbody%7Bbackground%2Dcolor%3A%23fff%3Bpadding%3A30px%3Bmargin%3A15px%3Bfont%2Dsize%3A14px%3Bline%2Dheight%3A1%2E6%7Dbody%3E%2A%3Afirst%2Dchild%7Bmargin%2Dtop%3A0%21important%7Dbody%3E%2A%3Alast%2Dchild%7Bmargin%2Dbottom%3A0%21important%7D%40media%20screen%7Bbody%7Bbox%2Dshadow%3A0%200%200%201px%20%23cacaca%2C0%200%200%204px%20%23eee%7D%7Dh1%2Ch2%2Ch3%2Ch4%2Ch5%2Ch6%7Bmargin%3A20px%200%2010px%3Bpadding%3A0%3Bfont%2Dweight%3Abold%3B%2Dwebkit%2Dfont%2Dsmoothing%3Asubpixel%2Dantialiased%3Bcursor%3Atext%7Dh1%7Bfont%2Dsize%3A28px%3Bcolor%3A%23000%7Dh2%7Bfont%2Dsize%3A24px%3Bborder%2Dbottom%3A1px%20solid%20%23ccc%3Bcolor%3A%23000%7Dh3%7Bfont%2Dsize%3A18px%3Bcolor%3A%23333%7Dh4%7Bfont%2Dsize%3A16px%3Bcolor%3A%23333%7Dh5%7Bfont%2Dsize%3A14px%3Bcolor%3A%23333%7Dh6%7Bcolor%3A%23777%3Bfont%2Dsize%3A14px%7Dp%2Cblockquote%2Ctable%2Cpre%7Bmargin%3A15px%200%7Dul%7Bpadding%2Dleft%3A30px%7Dol%7Bpadding%2Dleft%3A30px%7Dol%20li%20ul%3Afirst%2Dof%2Dtype%7Bmargin%2Dtop%3A0%7Dhr%7Bbackground%3Atransparent%20url%28data%3Aimage%2Fpng%3Bbase64%2CiVBORw0KGgoAAAANSUhEUgAAAAYAAAAECAYAAACtBE5DAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyJpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw%2FeHBhY2tldCBiZWdpbj0i77u%2FIiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8%2BIDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNSBNYWNpbnRvc2giIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6OENDRjNBN0E2NTZBMTFFMEI3QjRBODM4NzJDMjlGNDgiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6OENDRjNBN0I2NTZBMTFFMEI3QjRBODM4NzJDMjlGNDgiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo4Q0NGM0E3ODY1NkExMUUwQjdCNEE4Mzg3MkMyOUY0OCIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo4Q0NGM0E3OTY1NkExMUUwQjdCNEE4Mzg3MkMyOUY0OCIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI%2FPqqezsUAAAAfSURBVHjaYmRABcYwBiM2QSA4y4hNEKYDQxAEAAIMAHNGAzhkPOlYAAAAAElFTkSuQmCC%29%20repeat%2Dx%200%200%3Bborder%3A0%20none%3Bcolor%3A%23ccc%3Bheight%3A4px%3Bpadding%3A0%7Dbody%3Eh2%3Afirst%2Dchild%7Bmargin%2Dtop%3A0%3Bpadding%2Dtop%3A0%7Dbody%3Eh1%3Afirst%2Dchild%7Bmargin%2Dtop%3A0%3Bpadding%2Dtop%3A0%7Dbody%3Eh1%3Afirst%2Dchild%2Bh2%7Bmargin%2Dtop%3A0%3Bpadding%2Dtop%3A0%7Dbody%3Eh3%3Afirst%2Dchild%2Cbody%3Eh4%3Afirst%2Dchild%2Cbody%3Eh5%3Afirst%2Dchild%2Cbody%3Eh6%3Afirst%2Dchild%7Bmargin%2Dtop%3A0%3Bpadding%2Dtop%3A0%7Da%3Afirst%2Dchild%20h1%2Ca%3Afirst%2Dchild%20h2%2Ca%3Afirst%2Dchild%20h3%2Ca%3Afirst%2Dchild%20h4%2Ca%3Afirst%2Dchild%20h5%2Ca%3Afirst%2Dchild%20h6%7Bmargin%2Dtop%3A0%3Bpadding%2Dtop%3A0%7Dh1%2Bp%2Ch2%2Bp%2Ch3%2Bp%2Ch4%2Bp%2Ch5%2Bp%2Ch6%2Bp%2Cul%20li%3E%3Afirst%2Dchild%2Col%20li%3E%3Afirst%2Dchild%7Bmargin%2Dtop%3A0%7Ddl%7Bpadding%3A0%7Ddl%20dt%7Bfont%2Dsize%3A14px%3Bfont%2Dweight%3Abold%3Bfont%2Dstyle%3Aitalic%3Bpadding%3A0%3Bmargin%3A15px%200%205px%7Ddl%20dt%3Afirst%2Dchild%7Bpadding%3A0%7Ddl%20dt%3E%3Afirst%2Dchild%7Bmargin%2Dtop%3A0%7Ddl%20dt%3E%3Alast%2Dchild%7Bmargin%2Dbottom%3A0%7Ddl%20dd%7Bmargin%3A0%200%2015px%3Bpadding%3A0%2015px%7Ddl%20dd%3E%3Afirst%2Dchild%7Bmargin%2Dtop%3A0%7Ddl%20dd%3E%3Alast%2Dchild%7Bmargin%2Dbottom%3A0%7Dblockquote%7Bborder%2Dleft%3A4px%20solid%20%23DDD%3Bpadding%3A0%2015px%3Bcolor%3A%23777%7Dblockquote%3E%3Afirst%2Dchild%7Bmargin%2Dtop%3A0%7Dblockquote%3E%3Alast%2Dchild%7Bmargin%2Dbottom%3A0%7Dtable%7Bborder%2Dcollapse%3Acollapse%3Bborder%2Dspacing%3A0%3Bfont%2Dsize%3A100%25%3Bfont%3Ainherit%7Dtable%20th%7Bfont%2Dweight%3Abold%3Bborder%3A1px%20solid%20%23ccc%3Bpadding%3A6px%2013px%7Dtable%20td%7Bborder%3A1px%20solid%20%23ccc%3Bpadding%3A6px%2013px%7Dtable%20tr%7Bborder%2Dtop%3A1px%20solid%20%23ccc%3Bbackground%2Dcolor%3A%23fff%7Dtable%20tr%3Anth%2Dchild%282n%29%7Bbackground%2Dcolor%3A%23f8f8f8%7Dimg%7Bmax%2Dwidth%3A100%25%7Dcode%2Ctt%7Bmargin%3A0%202px%3Bpadding%3A0%205px%3Bwhite%2Dspace%3Anowrap%3Bborder%3A1px%20solid%20%23eaeaea%3Bbackground%2Dcolor%3A%23f8f8f8%3Bborder%2Dradius%3A3px%3Bfont%2Dfamily%3AConsolas%2C%27Liberation%20Mono%27%2CCourier%2Cmonospace%3Bfont%2Dsize%3A12px%3Bcolor%3A%23333%7Dpre%3Ecode%7Bmargin%3A0%3Bpadding%3A0%3Bwhite%2Dspace%3Apre%3Bborder%3A0%3Bbackground%3Atransparent%7D%2Ehighlight%20pre%7Bbackground%2Dcolor%3A%23f8f8f8%3Bborder%3A1px%20solid%20%23ccc%3Bfont%2Dsize%3A13px%3Bline%2Dheight%3A19px%3Boverflow%3Aauto%3Bpadding%3A6px%2010px%3Bborder%2Dradius%3A3px%7Dpre%7Bbackground%2Dcolor%3A%23f8f8f8%3Bborder%3A1px%20solid%20%23ccc%3Bfont%2Dsize%3A13px%3Bline%2Dheight%3A19px%3Boverflow%3Aauto%3Bpadding%3A6px%2010px%3Bborder%2Dradius%3A3px%7Dpre%20code%2Cpre%20tt%7Bbackground%2Dcolor%3Atransparent%3Bborder%3A0%7D%2Epoetry%20pre%7Bfont%2Dfamily%3AGeorgia%2CGaramond%2Cserif%21important%3Bfont%2Dstyle%3Aitalic%3Bfont%2Dsize%3A110%25%21important%3Bline%2Dheight%3A1%2E6em%3Bdisplay%3Ablock%3Bmargin%2Dleft%3A1em%7D%2Epoetry%20pre%20code%7Bfont%2Dfamily%3AGeorgia%2CGaramond%2Cserif%21important%3Bword%2Dbreak%3Abreak%2Dall%3Bword%2Dbreak%3Abreak%2Dword%3B%2Dwebkit%2Dhyphens%3Aauto%3B%2Dmoz%2Dhyphens%3Aauto%3Bhyphens%3Aauto%3Bwhite%2Dspace%3Apre%2Dwrap%7Dsup%2Csub%2Ca%2Efootnote%7Bfont%2Dsize%3A1%2E4ex%3Bheight%3A0%3Bline%2Dheight%3A1%3Bvertical%2Dalign%3Asuper%3Bposition%3Arelative%7Dsub%7Bvertical%2Dalign%3Asub%3Btop%3A%2D1px%7D%40media%20print%7Bbody%7Bbackground%3A%23fff%7Dimg%2Cpre%2Cblockquote%2Ctable%2Cfigure%7Bpage%2Dbreak%2Dinside%3Aavoid%7Dbody%7Bbackground%3A%23fff%3Bborder%3A0%7Dcode%7Bbackground%2Dcolor%3A%23fff%3Bcolor%3A%23333%21important%3Bpadding%3A0%20%2E2em%3Bborder%3A1px%20solid%20%23dedede%7Dpre%7Bbackground%3A%23fff%7Dpre%20code%7Bbackground%2Dcolor%3Awhite%21important%3Boverflow%3Avisible%7D%7D%40media%20screen%7Bbody%2Einverted%7Bcolor%3A%23eee%21important%3Bborder%2Dcolor%3A%23555%3Bbox%2Dshadow%3Anone%7D%2Einverted%20body%2C%2Einverted%20hr%20%2Einverted%20p%2C%2Einverted%20td%2C%2Einverted%20li%2C%2Einverted%20h1%2C%2Einverted%20h2%2C%2Einverted%20h3%2C%2Einverted%20h4%2C%2Einverted%20h5%2C%2Einverted%20h6%2C%2Einverted%20th%2C%2Einverted%20%2Emath%2C%2Einverted%20caption%2C%2Einverted%20dd%2C%2Einverted%20dt%2C%2Einverted%20blockquote%7Bcolor%3A%23eee%21important%3Bborder%2Dcolor%3A%23555%3Bbox%2Dshadow%3Anone%7D%2Einverted%20td%2C%2Einverted%20th%7Bbackground%3A%23333%7D%2Einverted%20h2%7Bborder%2Dcolor%3A%23555%7D%2Einverted%20hr%7Bborder%2Dcolor%3A%23777%3Bborder%2Dwidth%3A1px%21important%7D%3A%3Aselection%7Bbackground%3Argba%28157%2C193%2C200%2C0%2E5%29%7Dh1%3A%3Aselection%7Bbackground%2Dcolor%3Argba%2845%2C156%2C208%2C0%2E3%29%7Dh2%3A%3Aselection%7Bbackground%2Dcolor%3Argba%2890%2C182%2C224%2C0%2E3%29%7Dh3%3A%3Aselection%2Ch4%3A%3Aselection%2Ch5%3A%3Aselection%2Ch6%3A%3Aselection%2Cli%3A%3Aselection%2Col%3A%3Aselection%7Bbackground%2Dcolor%3Argba%28133%2C201%2C232%2C0%2E3%29%7Dcode%3A%3Aselection%7Bbackground%2Dcolor%3Argba%280%2C0%2C0%2C0%2E7%29%3Bcolor%3A%23eee%7Dcode%20span%3A%3Aselection%7Bbackground%2Dcolor%3Argba%280%2C0%2C0%2C0%2E7%29%21important%3Bcolor%3A%23eee%21important%7Da%3A%3Aselection%7Bbackground%2Dcolor%3Argba%28255%2C230%2C102%2C0%2E2%29%7D%2Einverted%20a%3A%3Aselection%7Bbackground%2Dcolor%3Argba%28255%2C230%2C102%2C0%2E6%29%7Dtd%3A%3Aselection%2Cth%3A%3Aselection%2Ccaption%3A%3Aselection%7Bbackground%2Dcolor%3Argba%28180%2C237%2C95%2C0%2E5%29%7D%2Einverted%7Bbackground%3A%230b2531%3Bbackground%3A%23252a2a%7D%2Einverted%20body%7Bbackground%3A%23252a2a%7D%2Einverted%20a%7Bcolor%3A%23acd1d5%7D%7D%2Ehighlight%20%2Ec%7Bcolor%3A%23998%3Bfont%2Dstyle%3Aitalic%7D%2Ehighlight%20%2Eerr%7Bcolor%3A%23a61717%3Bbackground%2Dcolor%3A%23e3d2d2%7D%2Ehighlight%20%2Ek%2C%2Ehighlight%20%2Eo%7Bfont%2Dweight%3Abold%7D%2Ehighlight%20%2Ecm%7Bcolor%3A%23998%3Bfont%2Dstyle%3Aitalic%7D%2Ehighlight%20%2Ecp%7Bcolor%3A%23999%3Bfont%2Dweight%3Abold%7D%2Ehighlight%20%2Ec1%7Bcolor%3A%23998%3Bfont%2Dstyle%3Aitalic%7D%2Ehighlight%20%2Ecs%7Bcolor%3A%23999%3Bfont%2Dweight%3Abold%3Bfont%2Dstyle%3Aitalic%7D%2Ehighlight%20%2Egd%7Bcolor%3A%23000%3Bbackground%2Dcolor%3A%23fdd%7D%2Ehighlight%20%2Egd%20%2Ex%7Bcolor%3A%23000%3Bbackground%2Dcolor%3A%23faa%7D%2Ehighlight%20%2Ege%7Bfont%2Dstyle%3Aitalic%7D%2Ehighlight%20%2Egr%7Bcolor%3A%23a00%7D%2Ehighlight%20%2Egh%7Bcolor%3A%23999%7D%2Ehighlight%20%2Egi%7Bcolor%3A%23000%3Bbackground%2Dcolor%3A%23dfd%7D%2Ehighlight%20%2Egi%20%2Ex%7Bcolor%3A%23000%3Bbackground%2Dcolor%3A%23afa%7D%2Ehighlight%20%2Ego%7Bcolor%3A%23888%7D%2Ehighlight%20%2Egp%7Bcolor%3A%23555%7D%2Ehighlight%20%2Egs%7Bfont%2Dweight%3Abold%7D%2Ehighlight%20%2Egu%7Bcolor%3A%23800080%3Bfont%2Dweight%3Abold%7D%2Ehighlight%20%2Egt%7Bcolor%3A%23a00%7D%2Ehighlight%20%2Ekc%2C%2Ehighlight%20%2Ekd%2C%2Ehighlight%20%2Ekn%2C%2Ehighlight%20%2Ekp%2C%2Ehighlight%20%2Ekr%7Bfont%2Dweight%3Abold%7D%2Ehighlight%20%2Ekt%7Bcolor%3A%23458%3Bfont%2Dweight%3Abold%7D%2Ehighlight%20%2Em%7Bcolor%3A%23099%7D%2Ehighlight%20%2Es%7Bcolor%3A%23d14%7D%2Ehighlight%20%2Ena%7Bcolor%3A%23008080%7D%2Ehighlight%20%2Enb%7Bcolor%3A%230086b3%7D%2Ehighlight%20%2Enc%7Bcolor%3A%23458%3Bfont%2Dweight%3Abold%7D%2Ehighlight%20%2Eno%7Bcolor%3A%23008080%7D%2Ehighlight%20%2Eni%7Bcolor%3A%23800080%7D%2Ehighlight%20%2Ene%2C%2Ehighlight%20%2Enf%7Bcolor%3A%23900%3Bfont%2Dweight%3Abold%7D%2Ehighlight%20%2Enn%7Bcolor%3A%23555%7D%2Ehighlight%20%2Ent%7Bcolor%3A%23000080%7D%2Ehighlight%20%2Env%7Bcolor%3A%23008080%7D%2Ehighlight%20%2Eow%7Bfont%2Dweight%3Abold%7D%2Ehighlight%20%2Ew%7Bcolor%3A%23bbb%7D%2Ehighlight%20%2Emf%2C%2Ehighlight%20%2Emh%2C%2Ehighlight%20%2Emi%2C%2Ehighlight%20%2Emo%7Bcolor%3A%23099%7D%2Ehighlight%20%2Esb%2C%2Ehighlight%20%2Esc%2C%2Ehighlight%20%2Esd%2C%2Ehighlight%20%2Es2%2C%2Ehighlight%20%2Ese%2C%2Ehighlight%20%2Esh%2C%2Ehighlight%20%2Esi%2C%2Ehighlight%20%2Esx%7Bcolor%3A%23d14%7D%2Ehighlight%20%2Esr%7Bcolor%3A%23009926%7D%2Ehighlight%20%2Es1%7Bcolor%3A%23d14%7D%2Ehighlight%20%2Ess%7Bcolor%3A%23990073%7D%2Ehighlight%20%2Ebp%7Bcolor%3A%23999%7D%2Ehighlight%20%2Evc%2C%2Ehighlight%20%2Evg%2C%2Ehighlight%20%2Evi%7Bcolor%3A%23008080%7D%2Ehighlight%20%2Eil%7Bcolor%3A%23099%7D%2Ehighlight%20%2Egc%7Bcolor%3A%23999%3Bbackground%2Dcolor%3A%23eaf2f5%7D%2Etype%2Dcsharp%20%2Ehighlight%20%2Ek%2C%2Etype%2Dcsharp%20%2Ehighlight%20%2Ekt%7Bcolor%3A%2300F%7D%2Etype%2Dcsharp%20%2Ehighlight%20%2Enf%7Bcolor%3A%23000%3Bfont%2Dweight%3Anormal%7D%2Etype%2Dcsharp%20%2Ehighlight%20%2Enc%7Bcolor%3A%232b91af%7D%2Etype%2Dcsharp%20%2Ehighlight%20%2Enn%7Bcolor%3A%23000%7D%2Etype%2Dcsharp%20%2Ehighlight%20%2Es%2C%2Etype%2Dcsharp%20%2Ehighlight%20%2Esc%7Bcolor%3A%23a31515%7D" />
  <!--[if lt IE 9]>
    <script src="//cdnjs.cloudflare.com/ajax/libs/html5shiv/3.7.3/html5shiv-printshiv.min.js"></script>
  <![endif]-->
</head>
<body>
<h1 id="change-log">Change log</h1>
<h2 id="winmerge-21640---2024-04-27">WinMerge 2.16.40 - 2024-04-27</h2>
<h3 id="general">General</h3>
<ul>
<li>BugFix: Fixed several Wine-related issues</li>
</ul>
<h3 id="file-compare">File compare</h3>
<ul>
<li>BugFix: Replace text issue (#2279)</li>
</ul>
<h3 id="options-dialog">Options dialog</h3>
<ul>
<li>Add a &quot;Defaults&quot; button to the &quot;Options (Editor &gt; General)&quot; dialog. (PR #2283)</li>
<li>Adjust position of &quot;Defaults&quot; button in &quot;Options &gt; Compare &gt; Table&quot; (PR #2295)</li>
</ul>
<h3 id="plugins">Plugins</h3>
<ul>
<li>BugFix: CompareMSExcelFiles.sct: Fixed the issue where, when the &#39;Extract workbook data to multiple files&#39; option was enabled, all sheets of .xlsx files containing more than 10 sheets were not compared. (This bug was introduced in version 2.16.39 beta.)</li>
<li>BugFix: insert datetime.sct: Fixed an issue where it didn&#39;t work properly depending on the locale. (This bug was introduced in version 2.16.39 beta.)</li>
<li>BugFix: Fixed an issue where a &quot;Catastrophic failure&quot; message box might have appeared when reloading a plugin while the Select Files or Folders window was displayed.</li>
<li>Allow plugin pipeline aliases or simple plugins to be registered in the GUI (PR #2257)</li>
</ul>
<h3 id="installer">Installer</h3>
<ul>
<li>BugFix: French encoding problem on the end of installation (last dialog box) (#2272)</li>
</ul>
<h3 id="translations">Translations</h3>
<ul>
<li>Translation updates:
<ul>
<li>Brazilian (PR #2289)</li>
<li>Catalan (PR #2270,#2274)</li>
<li>Chinese Simplified (PR #2290)</li>
<li>Corsican (PR #2288)</li>
<li>French (PR #2271,#2275,#2297)</li>
<li>Hungarian (PR #2291)</li>
<li>Japanese</li>
<li>Polish (PR#2277)</li>
</ul></li>
</ul>
<h2 id="winmerge-21639---2024-03-24">WinMerge 2.16.39 - 2024-03-24</h2>
<h3 id="general-1">General</h3>
<ul>
<li>BugFix: Fixed an issue where typing only a single character in the header bar could cause a crash.</li>
</ul>
<h3 id="file-compare-1">File compare</h3>
<ul>
<li>BugFix: Fixed possible crash</li>
<li>BugFix: Creation of .bak files fails when the original filename starts with a dot (#2217)</li>
<li>Update Java syntax highlighting keyword list. (PR #2215)</li>
<li>Replace &quot;Match similar lines&quot; with &quot;Align similar lines&quot; (PR #2230)</li>
<li>Make it possible to select the behavior when copying to another pane with &quot;Copy to Right&quot; etc. while text is selected. (PR #2224)</li>
<li>Make it possible to transform the text copied with &quot;Copy Right/Left&quot; using a plugin. (PR #2238)</li>
<li>Update PHP syntax highlighting keyword list. (PR #2265)</li>
</ul>
<h3 id="webpage-compare">Webpage compare</h3>
<ul>
<li>BugFix: Make event sync settings persistent (#2248)</li>
</ul>
<h3 id="select-files-or-folders-dialog">Select Files or Folders dialog</h3>
<ul>
<li>BugFix: Fixed the issue when the compare button could not be clicked when specifying an archive file and a regular file.</li>
</ul>
<h3 id="reports">Reports</h3>
<ul>
<li>BugFix: Fixed an issue where the widths of the left and right panes in HTML reports are not equal.</li>
<li>BugFix: Fixed an issue where the caption set in the header bar was not being applied to the HTML report.</li>
</ul>
<h3 id="plugins-1">Plugins</h3>
<ul>
<li>BugFix: Fixed crash when error occurs in Unpacker plugin</li>
<li>Replace the source code written in VBScript with JScript (PR #2098)</li>
<li>Add the ability to replace using patterns from Substitution Filters to the Replace plugin. (PR #2252)</li>
<li>Added sanity check for regular expressions in PrediffLineFilter plugin.</li>
</ul>
<h3 id="installer-1">Installer</h3>
<ul>
<li>BugFix: If the /noicons option was specified in the previous installation, a message box asking you to delete the previous start menu will be displayed in the next installation. (#2206)</li>
<li>Move custom messages in a separate iss file (#2247)</li>
</ul>
<h3 id="archive-support">Archive support</h3>
<ul>
<li>BugFix: 7zip encrypted archives with encrypted file names (#2225)</li>
</ul>
<h3 id="internals">Internals</h3>
<ul>
<li>Update codeql-analysis.yml - Version v2 to v3 (PR #2196)</li>
</ul>
<h3 id="translations-1">Translations</h3>
<ul>
<li>Translation updates:
<ul>
<li>Brazilian (PR #2234,#2243)</li>
<li>Chinese Simplified (PR #2241,#2244,#2251,#2256,#2259)</li>
<li>Corsican (PR #2266)</li>
<li>French (PR #2237,#2264)</li>
<li>Hungarian (PR #2232,#2250)</li>
<li>Italian (PR #2245,#2249)</li>
<li>Japanese</li>
<li>Korean (PR #2239)</li>
<li>Lithuanian (PR #2235,#2246)</li>
<li>Portuguese (PR #2263)</li>
<li>Russian (PR #2194,#2195,#2198,#2210,#2211,#2212)</li>
<li>Slovenian (#2096)</li>
</ul></li>
</ul>
<h2 id="winmerge-21638---2024-01-27">WinMerge 2.16.38 - 2024-01-27</h2>
<h3 id="file-compare-2">File compare</h3>
<ul>
<li>BugFix: Fixed an issue where changing the BOM in the right pane in the Codepage dialog was not reflected.</li>
<li>Update C++ syntax highlighting keyword list. (PR #2166)</li>
</ul>
<h3 id="webpage-compare-1">Webpage compare</h3>
<ul>
<li>During comparison, the status bar now displays &quot;Comparing...&quot;.</li>
<li>Add Location Pane (PR #2160)</li>
</ul>
<h3 id="folder-compare">Folder compare</h3>
<ul>
<li>Modify the &quot;Display Columns&quot; dialog. (PR #2154)</li>
</ul>
<h3 id="plugins-2">Plugins</h3>
<ul>
<li><p>BugFix: Fixed the issue where the following string containing double quotes is not interpreted correctly when specified to the <code>/unpacker</code> command line argument.</p>
<p><code>/unpacker &quot;Replace &quot;&quot;a&quot;&quot; &quot;&quot;b&quot;&quot;&quot;</code></p></li>
</ul>
<h3 id="translations-2">Translations</h3>
<ul>
<li>Translation updates:
<ul>
<li>Brazilian (PR #2151,#2178)</li>
<li>Chinese Simplified (PR #2153,#2183)</li>
<li>Corsican (PR #2180)</li>
<li>Hungarian (PR #2156,#2157,#2158)</li>
<li>Japanese</li>
<li>Korean (PR #2152)</li>
<li>Lithuanian (PR #2155,#2177)</li>
<li>Portuguese (PR #2185)</li>
</ul></li>
</ul>
<h2 id="winmerge-21637---2023-12-24">WinMerge 2.16.37 - 2023-12-24</h2>
<h3 id="file-compare-3">File compare</h3>
<ul>
<li>BugFix: Fixed an issue where the scroll position when clicking on Location View may not be as expected when Wrap Lines is enabled.</li>
<li>BugFix: Can&#39;t click and change file line endings with the version 2.16.36. (#2129)</li>
<li>BugFix: Unable to locate CR using \r in regular expression search</li>
<li>Added new C# keywords (PR #2136)</li>
</ul>
<h3 id="folder-compare-1">Folder compare</h3>
<ul>
<li>BugFix: Empty files are not copied (#2146)</li>
</ul>
<h3 id="binary-compare">Binary compare</h3>
<ul>
<li>BugFix: Could not replace data at the end of the file</li>
</ul>
<h3 id="image-compare">Image compare</h3>
<ul>
<li>BugFix: Fixed an issue where the file name was not displayed in the header even after saving a newly created pane with a name.</li>
</ul>
<h3 id="webpage-compare-2">Webpage compare</h3>
<ul>
<li>BugFix: Fixed an issue where the message box &quot;Another application has updated file ... since WinMerge scanned it last time&quot; is displayed when comparing URLs that are file://.</li>
<li>Improved synchronize events (winmerge/winwebdiff#4)</li>
</ul>
<h3 id="translations-3">Translations</h3>
<ul>
<li>Translation updates:
<ul>
<li>Bulgarian (PR #2124)</li>
<li>French (PR #2135,#2140,#2141,#2142,winmerge/frhed#15)</li>
<li>Italian (PR #2130)</li>
<li>Korean (PR #2126,#2127,#2143)</li>
<li>Polish (PR #2128)</li>
</ul></li>
</ul>
<h2 id="winmerge-21636---2023-11-27">WinMerge 2.16.36 - 2023-11-27</h2>
<h3 id="image-compare-1">Image compare</h3>
<ul>
<li>BugFix: Fix an issue where opening read-only and multi-page image files would cause them to be treated as only one-page images. (winmerge/winimerge#32)</li>
</ul>
<h3 id="webpage-compare-3">Webpage compare</h3>
<ul>
<li>Improved performance when there are many differences</li>
</ul>
<h3 id="file-filter">File filter</h3>
<ul>
<li>Bugfix: Modify the &quot;File Filters&quot; dialog. (#2118)</li>
</ul>
<h3 id="translations-4">Translations</h3>
<ul>
<li>Translation updates:
<ul>
<li>Corsican (PR #2123)</li>
<li>Galician (PR #2120)</li>
<li>Hungarian (PR #2122)</li>
<li>Portuguese (PR #2119)</li>
<li>Spanish (PR #2120)</li>
<li>Slovenian</li>
<li>Turkish (PR #2116)</li>
</ul></li>
</ul>
<h2 id="winmerge-21635---2023-11-20">WinMerge 2.16.35 - 2023-11-20</h2>
<h3 id="file-compare-4">File compare</h3>
<ul>
<li>BugFix: Fixed an issue where &quot;Copy to Left/Right and Advance&quot; would sometimes skip differences when comparing 3 files. (#1234)</li>
<li>BugFix: Print Preview buttons are not translatable (#2083,#2079)</li>
<li>BugFix: Fixed the issue where the caption set in the header bar is restored when the window is resized.</li>
</ul>
<h3 id="binary-compare-1">Binary compare</h3>
<ul>
<li>BugFix: V2.16.34 &quot;X86&quot; wired action (#2081)</li>
</ul>
<h3 id="image-compare-2">Image compare</h3>
<ul>
<li>BugFix: Close main window with &#39;Esc&#39; if there is only one MDI child window (#2084)</li>
</ul>
<h3 id="webpage-compare-4">Webpage compare</h3>
<ul>
<li>BugFix: Close main window with &#39;Esc&#39; if there is only one MDI child window (#2084)</li>
<li>Webpage Compare: synchronize events (#2111,#2064)</li>
</ul>
<h3 id="folder-compare-2">Folder compare</h3>
<ul>
<li>BugFix: Ignoring carriage return differences doesn&#39;t work anymore (#2080,#2099)</li>
</ul>
<h3 id="file-filters">File filters</h3>
<ul>
<li>BugFix: Fixed the issue where &quot;[F]&quot; could be set as the file filter if no file filter was selected in the Filters dialog.</li>
</ul>
<h3 id="installer-2">Installer</h3>
<ul>
<li>BugFix: fix bug of Registry path (PR #2086)</li>
<li>BugFix: &quot;Register Windows 11 Shell Extension&quot; fails because PowerShell script doesn&#39;t escape special chars correctly (#2109)</li>
</ul>
<h3 id="translations-5">Translations</h3>
<ul>
<li>Translation updates:
<ul>
<li>Brazilian (PR #2088,#2112)</li>
<li>Bulgarian (PR #2105)</li>
<li>Chinese Simplified (PR #2091,#2113)</li>
<li>French (PR #2106)</li>
<li>Galician (PR #2085,#2107)</li>
<li>Hungarian (PR #2093)</li>
<li>Japanese</li>
<li>Korean (PR #2092)</li>
<li>Lithuanian (PR #2090,#2114)</li>
<li>Polish (PR #2087)</li>
<li>Romanian (PR #2089,#2095)</li>
<li>Slovenian (#2096)</li>
<li>Spanish (PR #2085,#2107)</li>
<li>Turkish (PR #2076)</li>
</ul></li>
</ul>
<h2 id="winmerge-21634---2023-10-27">WinMerge 2.16.34 - 2023-10-27</h2>
<h3 id="file-compare-5">File compare</h3>
<ul>
<li>Allow NUL and \.\NUL in paths specified as command line arguments (PR #2056)</li>
<li>Added &quot;(F4)&quot; to the description of the toolbar icon &quot;Difference in the Current Line&quot;. (#2050)</li>
<li>Fix a crash problem when the Diff algorithm is set to something other than default and one file does not have an EOL of the last line and the other file has an EOL of the last line.</li>
</ul>
<h3 id="image-compare-3">Image compare</h3>
<ul>
<li>BugFix: Fixed the issue where .png is added when saving even if the file format does not use a converter such as jpg or png file.</li>
</ul>
<h3 id="folder-compare-3">Folder compare</h3>
<ul>
<li>BugFix: Folder compare omits unique folders from results if they contain any files and/or subfolders folder compare/merge (#2046)</li>
</ul>
<h3 id="line-filters">Line filters</h3>
<ul>
<li>Modify the &quot;Line Filters&quot; dialog. (PR #2047)</li>
</ul>
<h3 id="substitution-filters">Substitution filters</h3>
<ul>
<li>Modify the &quot;Substitution Filters&quot; dialog. (PR #2068)</li>
</ul>
<h3 id="translations-6">Translations</h3>
<ul>
<li>Translation updates:
<ul>
<li>French (PR #2055,#2058)</li>
<li>Korean (PR #2057,#2059,#2060)</li>
</ul></li>
</ul>
<h3 id="internals-1">Internals</h3>
<ul>
<li>Small code changes for CMDIChildWnd handling (#2043)</li>
</ul>
<h2 id="winmerge-21633---2023-09-20">WinMerge 2.16.33 - 2023-09-20</h2>
<h3 id="general-2">General</h3>
<ul>
<li>Reduce startup time and decrease the usage of Win32 user objects.</li>
</ul>
<h3 id="file-compare-6">File compare</h3>
<ul>
<li>BugFix: Cannot compare one-line file (#1972)</li>
<li>BugFix: &quot;Current Difference&quot; specified by double-clicking cannot merge using the &quot;Copy to Right (or Left)&quot; menu. (#1980)</li>
<li>BugFix: Wimerge saves changes to the wrong file (#1985) (PR #1988)</li>
<li>BugFix: &quot;Ignore comment differences&quot; still compares inline comments (#2008)</li>
<li>Update Rust syntax highlighting keyword list. (PR #1998)</li>
<li>[Feature Request] Both Shell Menu (#1986) (PR #2021)</li>
</ul>
<h3 id="table-compare">Table compare</h3>
<ul>
<li>When &quot;Use First Line as Header&quot; is enabled, make the header display the first line regardless of the scroll position when the first line is hidden.</li>
<li>Generate reports in tabular format for table comparisons. (PR #1983)</li>
</ul>
<h3 id="folder-compare-4">Folder compare</h3>
<ul>
<li>BugFix: Fixed an issue where Differences, Left/Right EOL columns, etc. were displayed as undefined values when the file comparison method was Full Contents or Quick Contents and the file size exceeded 64MB.</li>
<li>BugFix: Fix the problem that when comparing with the BinaryContents compare method, the contents of the files are identical, but if one side is a symbolic link, it is judged to be different. (#1976)</li>
<li>BugFix: Fixed an issue where values in the Left/Right EOL column may not display correctly when using the Quick contents compare method.</li>
<li>Add Expand Different Subfolders menu item (#1382) (PR #1964)</li>
<li>Allow Diff algorithms (patience, histogram) other than default to be applied to folder comparisons (PR #2015) (#2002)</li>
<li>Show confirmation message when closing a window that took a long time to compare folders</li>
</ul>
<h3 id="line-filters-1">Line filters</h3>
<ul>
<li>Improve line filters and substitution filters (PR #2032) (#796) (#1620)</li>
</ul>
<h3 id="substitution-filters-1">Substitution filters</h3>
<ul>
<li>Avoid infinite loops in the RegularExpression::subst() function when the length of the string matching the pattern is 0</li>
<li>Improve line filters and substitution filters (PR #2032) (#796) (#1620)</li>
</ul>
<h3 id="options-dialog-1">Options dialog</h3>
<ul>
<li>Execute the &quot;pause&quot; command to prevent the error message from disappearing if the registration of the ShellExtension for Windows 11 fails</li>
</ul>
<h3 id="plugins-3">Plugins</h3>
<ul>
<li>BugFix: WinMerge cannot successfully disable some of its Plugins (#2012)</li>
<li>Update jq to version 1.7</li>
</ul>
<h3 id="manual">Manual</h3>
<ul>
<li>Manual: Use po4a for manual translation (PR #1994) (#499)</li>
</ul>
<h3 id="translations-7">Translations</h3>
<ul>
<li>Translation updates:
<ul>
<li>Brazilian (PR #1969,#2001,#2025)</li>
<li>Chinese Simplified (PR #1953,#1971,#2017,#2026)</li>
<li>Corsican (PR #2022)</li>
<li>German (PR #1952,#1977,#1989)</li>
<li>Hungarian (PR #1968,#1999)</li>
<li>Japanese</li>
<li>Korean (PR #1979,#2030)</li>
<li>Lithuanian (PR #1974,#2018,#2027)</li>
<li>Polish (PR #1990)</li>
<li>Portuguese (PR #1973,#2014)</li>
<li>Slovenian</li>
<li>Ukrainian (PR #1955)</li>
</ul></li>
</ul>
<h3 id="internals-2">Internals</h3>
<ul>
<li>Optimize inserts in std containers using reserve (PR #2000)</li>
</ul>
<h2 id="winmerge-21632---2023-07-27">WinMerge 2.16.32 - 2023-07-27</h2>
<h3 id="general-3">General</h3>
<ul>
<li>BugFix: Export/Import settings bug with Substitution Filters (#1925)</li>
</ul>
<h3 id="file-compare-7">File compare</h3>
<ul>
<li>BugFix: Save function doesn&#39;t work if the path length exceeds 248 characters (#1923)</li>
<li>BugFix: Redundant confirmation &quot;The selected files are identical&quot; (#1902)</li>
<li>Update Python syntax highlighting keyword list. (PR #1938)</li>
</ul>
<h3 id="folder-compare-5">Folder compare</h3>
<ul>
<li>BugFix: Treeview scrolls to the wrong position. (#1915)</li>
<li>Allow changing the number of CPU cores to use while doing folder comparison (PR #1945)</li>
</ul>
<h3 id="webpage-compare-5">Webpage compare</h3>
<ul>
<li>Add support for generating report files (PR #1941)</li>
</ul>
<h3 id="command-line">Command line</h3>
<ul>
<li>Compare folders recursively if &quot;Include subfolders&quot; is checked in the Options dialog even if the /r command line option is not specified. (PR #1914)</li>
</ul>
<h3 id="archive-support-1">Archive support</h3>
<ul>
<li>Update 7-Zip to 23.01 (PR #1913)</li>
</ul>
<h3 id="translations-8">Translations</h3>
<ul>
<li>New translation: Tamil (PR #1946)</li>
<li>Translation updates:
<ul>
<li>Brazilian (PR #1948)</li>
<li>Chinese Traditional (PR #1940)</li>
<li>Corsican (PR #1933)</li>
<li>French (PR #1927,#1928,#1951)</li>
<li>Korean (PR #1908)</li>
<li>Lithuanian (PR #1949)</li>
<li>Portuguese (PR #1930)</li>
<li>Slovenian</li>
<li>Turkish (#1931)</li>
</ul></li>
</ul>
<h2 id="winmerge-21631---2023-06-20">WinMerge 2.16.31 - 2023-06-20</h2>
<h3 id="general-4">General</h3>
<ul>
<li>BugFix: Some Substitution filter doesn&#39;t work (#1861)</li>
<li>Add tasks to Jump List (PR #1828)</li>
<li>Update DirCmpReport.cpp (PR #1892)</li>
</ul>
<h3 id="file-compare-8">File compare</h3>
<ul>
<li>BugFix: Fix input range check processing in &quot;Go to&quot; dialog. (PR #1826)</li>
<li>BugFix: End of line diff is a bit wanky (#1838, PR #1849)</li>
<li>Confirm copy all in file merge (PR #1827)</li>
<li>Modify the &quot;Go to&quot; dialog. (PR #1896)</li>
</ul>
<h3 id="folder-compare-6">Folder compare</h3>
<ul>
<li>BugFix: Display problem with Item totals : (#1840)</li>
<li>BugFix: Bug in ignore whitespace ? (#1882)</li>
</ul>
<h3 id="plugins-4">Plugins</h3>
<ul>
<li>PrettifyJSON: Update jq to version 1.6 (#1871)</li>
<li>Translate some plugin error messages (PR #1873)</li>
<li>ApplyPatch: Update GNU patch to 2.7.6-1 (PR #1897)(#1871)</li>
</ul>
<h3 id="installer-3">Installer</h3>
<ul>
<li>Silent install blocked (#1852)</li>
</ul>
<h3 id="translations-9">Translations</h3>
<ul>
<li>Translation updates:
<ul>
<li>Brazilian (PR #1829,#1837,#1876,#1903)</li>
<li>Bulgarian (PR #1822)</li>
<li>Chinese Simplified (PR #1835,#1846,#1885,#1906)</li>
<li>Dutch (PR #1831)</li>
<li>French (PR #1841,#1842,#1894)</li>
<li>Galician (PR #1833)</li>
<li>German (PR #1850,#1875,#1907)</li>
<li>Hungarian (PR #1832,#1839,#1845,#1878,#1905)</li>
<li>Japanese</li>
<li>Korean (PR #1820,#1821,#1877)</li>
<li>Lithuanian (PR #1847,#1889,#1904)</li>
<li>Polish (PR #1869,#1870,#1884)</li>
<li>Portuguese (PR #1843,#1895)</li>
<li>Spanish (PR #1834)</li>
<li>Russian (PR #1824,#1825,#1862)</li>
</ul></li>
</ul>
<h2 id="winmerge-21630---2023-04-27">WinMerge 2.16.30 - 2023-04-27</h2>
<h3 id="general-5">General</h3>
<ul>
<li>BugFix: When using an ini file all differences are displayed as black sections (#1799)</li>
<li>Reduced file size of WinMergeU.exe for 32-bit version</li>
</ul>
<h3 id="file-compare-9">File compare</h3>
<ul>
<li>BugFix: Fixed an issue where XML format files, such as vcxproj, were not being syntax highlighted.</li>
<li>BugFix: Fixed an issue where the EOL character was not being displayed in the ARM64 version.</li>
<li>Modify the &quot;Go to&quot; dialog. (PR #1779)</li>
</ul>
<h3 id="image-compare-4">Image compare</h3>
<ul>
<li>BugFix: Middle Click to close image tab crash (#1785)</li>
<li>BugFix: Fixed an issue where the changed Color distance threshold value would revert back to 0 the next time WinMerge was started.</li>
<li>Image compare: Increase color distance threshold (CD Threshold) (winmerge/winimerge #29)</li>
</ul>
<h3 id="filters">Filters</h3>
<ul>
<li>Updated file filters to allow UTF-8 without BOM.</li>
</ul>
<h3 id="options-dialog-2">Options dialog</h3>
<ul>
<li>BugFix: Fixed an issue where the shell integration category page in the Options dialog was not displaying correctly in version 2.16.29.</li>
<li>BugFix: Fixed an issue where plugin settings were not exported when exporting from the Options dialog.</li>
</ul>
<h3 id="plugins-5">Plugins</h3>
<ul>
<li>BugFix: Fixed an issue where the ApplyPatch plugin was not functioning correctly when the &#39;Plugins -&gt; Manual Unpacking&#39; menu item was checked.</li>
<li>BugFix: Fixed the problem that the plug-in setting window cannot be opened on 32-bit OS</li>
<li>Added a &quot;Plugin Settings&quot; button to the &quot;Select Plugin&quot; window.</li>
<li>Make the plugin settings dialog translatable and enable saving settings to an INI file (PR #1783)</li>
</ul>
<h3 id="command-line-1">Command line</h3>
<ul>
<li>BugFix: Fixed the problem that the /fr option is ignored when specifying the command line option /new</li>
<li>BugFix: Fixed the problem of not moving to the conflicted line after auto-merge.</li>
</ul>
<h3 id="project-file">Project file</h3>
<ul>
<li>BugFix: Opening a project fails when a path has environmental variables in it. (#1793)</li>
</ul>
<h3 id="installer-4">Installer</h3>
<ul>
<li>BugFix: Unsuccessful installation, Portable Win32 version (#1802)</li>
</ul>
<h3 id="translations-10">Translations</h3>
<ul>
<li>Translation updates:
<ul>
<li>Brazilian (PR #1806)</li>
<li>Chinese Simplified (PR #1808)</li>
<li>German (PR #1807)</li>
<li>Hungarian (PR #1809)</li>
<li>Japanese</li>
<li>Korean (PR #1774,#1788,#1790,#1791)</li>
<li>Lithuanian (PR #1817)</li>
<li>Polish (PR #1815)</li>
<li>Portuguese (PR #1787)</li>
<li>Slovenian (#1812)</li>
</ul></li>
</ul>
<h2 id="winmerge-21629---2023-03-21">WinMerge 2.16.29 - 2023-03-21</h2>
<h3 id="file-compare-10">File compare</h3>
<ul>
<li>BugFix: 3-way compare does not properly align identical lines when resolving conflicts, and show false highlights (#1696)</li>
<li>BugFix: Failure to indent lines properly (#1740)</li>
<li>BugFix: Fixed problems with non-default Diff algorithm:
<ul>
<li>Even though the Ignore carriage return differences option was disabled, EOL differences were ignored when the option to ignore whitespace differences was enabled.</li>
<li>Differences between CR and CR+LF were not ignored even though Ignore carriage return differences option was enabled.</li>
</ul></li>
<li>BugFix: Fixed a bug with Match whole word only options of Substitution filters</li>
<li>Add MATLAB syntax highlighting. (PR #1766)</li>
</ul>
<h3 id="table-compare-1">Table compare</h3>
<ul>
<li>The &quot;View &gt; Wrap Lines&quot; menu item in the Table Compare window is now &quot;Wrap Text&quot; and its check status is saved separately from the same menu item in the Text Compare window. (osdn #47553)</li>
<li>Added an option to change the CSV file separator from comma to semicolon or another character.</li>
</ul>
<h3 id="binary-compare-2">Binary compare</h3>
<ul>
<li>BugFix: Resizing WinMerge Window looses the correct scroll position for the memory address your cursor is at (WinMerge/frhed #13)</li>
<li>BugFix: Fixed an issue where an infinite loop occurred when the file size was truncated during file loading.</li>
</ul>
<h3 id="image-compare-5">Image compare</h3>
<ul>
<li>BugFix: Fixed issue preventing saving image comparison reports to a network share</li>
</ul>
<h3 id="webpage-compare-6">Webpage compare</h3>
<ul>
<li>BugFix: Fixed issue with missing file path in header bar</li>
</ul>
<h3 id="folder-compare-7">Folder compare</h3>
<ul>
<li>BugFix: After I collapse a folder, the arrow next to it stays turned down (#1747)</li>
<li>BugFix: Fixed an issue where the Left/Right Date and Left/Right Size columns would not update when deleting files on one side.</li>
<li>Implement issue #1413: &quot;Move&quot; needs options &quot;Left to Right&quot; or &quot;Right to Left&quot; like &quot;Copy&quot; (PR #1732,#1720)</li>
<li>Pressing F2 or Rename should not select file extension (#1735)</li>
</ul>
<h3 id="options-dialog-3">Options dialog</h3>
<ul>
<li>Added Enable Compare As menu option in Shell integration category</li>
</ul>
<h3 id="plugins-6">Plugins</h3>
<ul>
<li>BugFix: Plugin IgnoreLeadingLineNumbers hangs (#1715)</li>
</ul>
<h3 id="command-line-2">Command line</h3>
<ul>
<li>BugFix: Fixed a crash that occurred when comparing alternate data streams, such as file.zip:Zone.Identifier:$DATA, using the /self-compare command line option.</li>
<li>BugFix: Fixed a crash that occurred when the /t command line option was specified with only one path provided</li>
</ul>
<h3 id="shell-extension">Shell extension</h3>
<ul>
<li>BugFix: Windows 11 Tabs - wrong folder selected from new menu (#1733)</li>
<li>BugFix: Fixed the issue where the WinMerge menu would not appear when right-clicking on a non-item area in Windows 11 Explorer.</li>
<li>Added Compare As menu item</li>
</ul>
<h3 id="installer-5">Installer</h3>
<ul>
<li>Installer: components step unclear about &quot;Patch GnuWin32&quot; (#1698)</li>
<li>The ShellExtension*.dll file is now renamed before installation to prevent installation failure when Explorer is loading ShellExtension*.dll.</li>
<li>Added IgnoreLeadingLineNumbers plugin to non-x86 installers</li>
</ul>
<h3 id="translations-11">Translations</h3>
<ul>
<li>BugFix: Fix an issue where some messages are not translated. (PR #1712)</li>
<li>Translation updates:
<ul>
<li>Brazilian (PR #1711,#1713,#1727,#1728,#1756)</li>
<li>Chinese Simplified (PR #1714,#1719,#1722,#1731,#1765)</li>
<li>Corsican (PR #1717,#1723,#1752)</li>
<li>Dutch (PR #1757)</li>
<li>German (PR #1730,#1754)</li>
<li>Hungarian (PR #1725,#1726,#1758)</li>
<li>Japanese</li>
<li>Korean (PR #1689,#1690,#1692,#1693,#1694,#1718)</li>
<li>Lithuanian (PR #1729,#1755)</li>
<li>Polish (PR #1763,#1764)</li>
<li>Portuguese (PR #1737)</li>
<li>Russian (PR #1710,#1751)</li>
<li>Swedish (#1706,PR #1707)</li>
</ul></li>
</ul>
<h2 id="winmerge-21628---2023-02-15">WinMerge 2.16.28 - 2023-02-15</h2>
<h3 id="folder-compare-8">Folder compare</h3>
<ul>
<li>BugFix: Fixed an issue where files with no extension were not compared if they were in a folder with a &#39;.&#39; in the folder name.</li>
</ul>
<h2 id="winmerge-21626---2023-01-27">WinMerge 2.16.26 - 2023-01-27</h2>
<h3 id="general-6">General</h3>
<ul>
<li>Fixed issue where the program would crash when certain path names were set in the file path bar.</li>
</ul>
<h3 id="file-compare-11">File compare</h3>
<ul>
<li>Feature request: Allow pasting when editing caption of pages (PR #1651)</li>
</ul>
<h3 id="folder-compare-9">Folder compare</h3>
<ul>
<li>BugFix: Filters aren&#39;t saved anywhere (#1638)</li>
<li>BugFix: Fixed issue where the Open menu item in file path bar of folder comparison window was disabled.</li>
<li>Add processing to indicate that two directories are identical in the &quot;Comparison result&quot; column when they are identical in a 3-way folder comparison. (PR #1649)</li>
<li>Request: highlight the file after opening its parent folder (#1662)</li>
<li>Show/hide directories in 3-way comparison (PR #1683)</li>
</ul>
<h3 id="binary-compare-3">Binary compare</h3>
<ul>
<li>BugFix: Fixed issue where the Open menu item in file path bar of binary comparison window was disabled.</li>
</ul>
<h3 id="webpage-compare-7">Webpage compare</h3>
<ul>
<li>BugFix: Deleted color of Word Difference in Options dialog was not used.</li>
<li>Implemented Ignore numbers comparison option.</li>
</ul>
<h3 id="options-dialog-4">Options dialog</h3>
<ul>
<li>Modify the &quot;Options (Compare &gt; Folder)&quot; dialog. (PR #1645)</li>
</ul>
<h3 id="plugins-7">Plugins</h3>
<ul>
<li>Add PreviewMarkdown plugin (PR #1641)</li>
<li>Add PreviewPlantUML plugin (PR #1666)</li>
<li>CompareMSExcelFiles: Added &quot;Compare worksheets as HTML&quot; in CompareMSExcelFiles plugin options window</li>
<li>ApacheTika: Updated Apache Tika to version 2.6.0</li>
<li>ApacheTika: If Java is not installed, OpenJDK 19.0.2 will now be downloaded and used.</li>
</ul>
<h3 id="translations-12">Translations</h3>
<ul>
<li>Translation updates:
<ul>
<li>Brazilian (PR #1656,#1670)</li>
<li>Chinese Simplified (PR #1635,#1665,#1667,#1677,#1681)</li>
<li>Corsican (PR #1674)</li>
<li>French (PR #1640,#1679)</li>
<li>German (PR #1660,#1671)</li>
<li>Hungarian (PR #1664)</li>
<li>Japanese</li>
<li>Lithuanian (PR #1657,#1673)</li>
<li>Polish (PR #1648)</li>
<li>Portuguese (PR #1669)</li>
<li>Russian (PR #1676)</li>
<li>Slovenian</li>
<li>Swedish (PR #1655,#1663,#1682)</li>
</ul></li>
</ul>
<h2 id="winmerge-21625---2022-12-27">WinMerge 2.16.25 - 2022-12-27</h2>
<h3 id="file-compare-12">File compare</h3>
<ul>
<li>BugFix: Selection in &quot;Replace&quot; by regular expression doesn&#39;t work with <code>\n</code> (#1556)</li>
<li>BugFix: WinMerge hangs for a certain regex search &amp; replace action for clearing all lines not containing &#39;%&#39; (#1575)</li>
<li>Add html5 keywords to crystaledit (PR #1565)</li>
<li>Add css keywords to crystaledit (PR #1572)</li>
<li>Preliminary exit (performance optimization) for Scrollbars calculation (PR #1574, #1573)</li>
<li>Fix issue #1583 Syntax highlighting for SQL leaves many things out. (PR #1591, #1583)</li>
<li>Starting Pane Consistency (#1598)</li>
</ul>
<h3 id="binary-compare-4">Binary compare</h3>
<ul>
<li>BugFix: Crash 0xc0000409 (#1544)</li>
<li>Binary compare: Allow 64bit versions to open files larger than 2GB (PR #1549)</li>
</ul>
<h3 id="folder-compare-10">Folder compare</h3>
<ul>
<li>BugFix: Fix the problem that WinMerge crashes when pressing the &quot;OK&quot; button in the &quot;Display Columns&quot; dialog in the debug version. (PR #1568)</li>
<li>BugFix: Crash when copying files/folders (#1558)</li>
<li>BugFix: File Duplication Bug In Outputted Zips (#1588)</li>
<li>BugFix: Fixed problem with scrolling to unexpected position when expanding folders (osdn.net #46061)</li>
<li>BugFix: Fixed incorrect links to files with # in filename in folder comparison report (osdn.net #46082)</li>
<li>Changes the display processing of the &quot;Comparison result&quot; column for a 3-way folder comparison. (PR #1545)</li>
<li>Add &quot;Copy All Displayed Columns&quot; to the context menu of the folder compare window. (PR #1615)</li>
</ul>
<h3 id="options-dialog-5">Options dialog</h3>
<ul>
<li>Added Auto-reload modified files option (PR #1611)</li>
</ul>
<h3 id="translations-13">Translations</h3>
<ul>
<li>Translation updates:
<ul>
<li>Brazilian (PR #1617,#1630)</li>
<li>Chinese Simplified (PR #1614)</li>
<li>Corsican (PR #1628,#1629)</li>
<li>Galician (#1581)</li>
<li>German (PR #1616,#1633)</li>
<li>Hungarian (PR #1618,#1631)</li>
<li>Lithuanian (PR #1621,#1632)</li>
<li>Japanese</li>
<li>Polish (PR #1566)</li>
<li>Russian (PR #1553,#1554,#1555)</li>
<li>Slovenian</li>
<li>Swedish (PR #1594)</li>
<li>Turkish (PR #1563)</li>
</ul></li>
</ul>
<h3 id="others">Others</h3>
<ul>
<li>png optimization(loss less) (PR #1541)</li>
<li>Fields rearranged for size optimization (PR #1576)</li>
<li>refactoring Diff3.h (PR #1577)</li>
<li>Fix: Mismatch between keyword list and comments (PR #1578)</li>
<li>DiffFileData optimization for same-files (PR #1579)</li>
<li>Fixed potentially wrong SubEditList ReadOnly attribute storage; refactoring (PR #1580)</li>
<li>CheckForInvalidUtf8 performance improvement; code cleanup (PR #1582)</li>
<li>Update unicoder.cpp (PR #1584)</li>
<li>unicoder.cpp light performance improvements (PR #1586)</li>
<li>Update markdown.cpp (PR #1590)</li>
<li>Add a feature for debugging. (PR #1595)</li>
</ul>
<h2 id="winmerge-21624---2022-10-27">WinMerge 2.16.24 - 2022-10-27</h2>
<h3 id="general-7">General</h3>
<ul>
<li>BugFix: Fixed crash when displaying file menu if jump list contains invalid title (osdn.net #45916)</li>
</ul>
<h3 id="file-compare-13">File compare</h3>
<ul>
<li>Changed operation of displaying dialogs and context menus from status bar from double-click to single-click.</li>
</ul>
<h3 id="table-compare-2">Table compare</h3>
<ul>
<li>BugFix: when TSV files were displayed in table mode with word wrap enabled, clicking on a character would not move the caret to that character&#39;s position</li>
</ul>
<h3 id="folder-compare-11">Folder compare</h3>
<ul>
<li>Fixed memory leak in folder comparison when PDF files were targeted for image comparison.</li>
</ul>
<h3 id="options-dialog-6">Options dialog</h3>
<ul>
<li>Improved translation regarding CPU cores (PR #1513)</li>
</ul>
<h3 id="select-files-or-folders-dialog-1">Select Files or Folders dialog</h3>
<ul>
<li>Made it possible to specify Prediffer plugin</li>
</ul>
<h3 id="plugins-8">Plugins</h3>
<ul>
<li>BugFix: Select Plugin dialog: Fixed that &quot;Display all plugins&quot; checkbox did not work</li>
</ul>
<h3 id="project-file-1">Project file</h3>
<ul>
<li>BugFix: Fixed comparison failure when left file path and right file path in project file are URLs.</li>
<li>Allow saving following in project file.
<ul>
<li>Description</li>
<li>Window type</li>
<li>Table delimiter</li>
</ul></li>
</ul>
<h3 id="patch-generator-dialog">Patch Generator dialog</h3>
<ul>
<li>BugFix: The command line section in the generated patch file was garbled (osdn.net #45935)</li>
</ul>
<h3 id="translations-14">Translations</h3>
<ul>
<li>BugFix: Fix an issue where the following message displayed when two files are identical in a 3-way folder comparison is not translated. (PR #1535)</li>
<li>Translation updates:
<ul>
<li>Brazilian (PR #1511,#1523)</li>
<li>Corsican (PR #1510,#1527)</li>
<li>French (PR #1538)</li>
<li>German (PR #1498,#1509,#1528)</li>
<li>Hungarian (PR #1508,#1524)</li>
<li>Japanese</li>
<li>Lithuanian (PR #1514,#1526)</li>
<li>Polish (PR #1525)</li>
<li>Portuguese (PR #1529)</li>
<li>Slovenian</li>
</ul></li>
</ul>
<h3 id="others-1">Others</h3>
<ul>
<li>BugFix: Fixed English verbiage (PR #1499)</li>
<li>BugFix: typo fixed in README.md (PR #1504)</li>
<li>BugFix: Fix typo in lwdisp.c (PR #1515)</li>
</ul>
<h2 id="winmerge-21623---2022-09-26">WinMerge 2.16.23 - 2022-09-26</h2>
<h3 id="general-8">General</h3>
<ul>
<li>BugFix: Fix an issue where filenames containing &quot;&amp;&quot; are not displayed properly in the MDI tab bar and its tooltips. (PR #1466)</li>
</ul>
<h3 id="color-schemes">Color schemes</h3>
<ul>
<li>Create Midnight.ini (PR #1430)</li>
</ul>
<h3 id="file-compare-14">File compare</h3>
<ul>
<li>BugFix: Non existing backup directory should be automatically created (#1438)</li>
<li>BugFix: Bug: Can&#39;t copy selected text, if it has non-changed lines (#1507)</li>
<li>Remember zoom level for all files (#1433)</li>
<li>The feature will allow the user to right-click the selected lines and… add them to Line Filter so that those lines added to the Line Filter will be ignored if found in any file. (PR #1481)</li>
<li>CrystalEdit/parsers/SQL: Added more keywords (PR #1493)</li>
</ul>
<h3 id="table-compare-3">Table compare</h3>
<ul>
<li>Bugfix: Inline differences ware not displayed even if the caret is moved to the position of an inline difference that is hidden due to the narrow column width.</li>
</ul>
<h3 id="webpage-compare-8">Webpage compare</h3>
<ul>
<li>[EXPERIMENTAL] Webpage Compare: Highlight differences (PR #1357)</li>
</ul>
<h3 id="folder-compare-12">Folder compare</h3>
<ul>
<li>BugFix: Disable rename operations when in read-only mode in the folder compare window. (PR #1434)</li>
<li>BugFix: Fix an issue where renaming to a file name or directory name containing &quot;&quot; or &quot;/&quot; is not done properly. (PR #1451)</li>
<li>BugFix: Fix &quot;Left to Right&quot; and &quot;Right to Left&quot; copying in 2-way folder comparison. (PR #1495)</li>
<li>BugFix: Folder compare with jpg images crashes (#1176) (Previous versions were linked with unfixed freeimage.)</li>
</ul>
<h3 id="archive-support-2">Archive support</h3>
<ul>
<li>Update 7-Zip to 22.01 (#1425)</li>
</ul>
<h3 id="translations-15">Translations</h3>
<ul>
<li>Translation updates:
<ul>
<li>Brazilian (PR #1436,#1437,#1441,#1459,#1463)</li>
<li>Corsican (PR #1443,#1480,#1486)</li>
<li>Dutch (PR #1474)</li>
<li>Finnish (PR #1460)</li>
<li>French (PR #1491)</li>
<li>German (PR #1455,#1484)</li>
<li>Hungarian (PR #1431,#1454)</li>
<li>Japanese</li>
<li>Lithuanian (PR #1457,#1485)</li>
<li>Polish (PR #1427,#1456)</li>
<li>Portuguese (PR #1453,#1490)</li>
<li>Russian (PR #1426)</li>
<li>Slovenian (#1424,PR #1461)</li>
<li>Spanish (PR #1406)</li>
</ul></li>
</ul>
<h2 id="winmerge-21622---2022-07-27">WinMerge 2.16.22 - 2022-07-27</h2>
<h3 id="general-9">General</h3>
<ul>
<li>Allow renaming of untitled pages (#1395)</li>
</ul>
<h3 id="file-compare-15">File compare</h3>
<ul>
<li>BugFix: &quot;Replace All&quot; doesn&#39;t work when Replace in &quot;Selection&quot; and the new string contains the old string. (#1376)</li>
<li>BugFix: “Match case” in Search always enabled (#1380)</li>
<li>BugFix: vertical editing for .h file is quite slow (#1386)</li>
<li>BugFix: replace text using regular expressions behaves incorrectly if multiple matches on the same line (#1387, PR #1388)</li>
<li>Optimize snake function (PR #1411)</li>
</ul>
<h3 id="folder-compare-13">Folder compare</h3>
<ul>
<li>BugFix: Fix an issue where paths with different case are not displayed correctly in the folder column of the folder compare window when comparing three directories. (PR #1372)</li>
<li>BugFix: Fix renaming process in folder compare window. (PR #1392)</li>
<li>BugFix: Elapsed time was no longer displayed in the status bar after folder comparison.</li>
<li>BugFix: Fix an issue where the folder column is not updated for child items after renaming a directory in the folder compare window. (PR #1408)</li>
</ul>
<h3 id="plugins-9">Plugins</h3>
<ul>
<li>Modify textbox behavior (CompareMSExcelFiles options dialog) (PR #1374)</li>
<li>Make wsc files available as plug-in (PR #1390)</li>
</ul>
<h3 id="archive-support-3">Archive support</h3>
<ul>
<li>Update 7-Zip to 22.00</li>
</ul>
<h3 id="project-file-2">Project file</h3>
<ul>
<li>Add a feature to save/restore hidden items to/from a project file.(PR #1377)</li>
</ul>
<h3 id="options-dialog-7">Options dialog</h3>
<ul>
<li>New setting to decide when to save/restore hidden items when project is saved/loaded (PR #1377)</li>
</ul>
<h3 id="translations-16">Translations</h3>
<ul>
<li>Translation updates:
<ul>
<li>Bulgarian (PR #1375)</li>
<li>French (PR #1418)</li>
<li>Galician (PR #1400)</li>
<li>German (PR #1396,#1399)</li>
<li>Hungarian (PR #1393,#1398)</li>
<li>Japanese</li>
<li>Lithuanian (PR #1394)</li>
<li>Portuguese (PR #1416)</li>
</ul></li>
</ul>
<h3 id="internals-3">Internals</h3>
<ul>
<li>Fix typo in SuperComboBox.cpp (PR #1379)</li>
</ul>
<h2 id="winmerge-21621---2022-06-20">WinMerge 2.16.21 - 2022-06-20</h2>
<h3 id="general-10">General</h3>
<ul>
<li>In windows 11 i have error 78 sidebyside with the winmerge manifest (#1312)</li>
</ul>
<h3 id="file-compare-16">File compare</h3>
<ul>
<li>BugFix: Copy left/right different behavior (#1334)</li>
<li>BugFix: Line difference coloring in “Word-level” mode does not work correctly for Cyrillic-based languages (#1362)</li>
<li>BugFix: Syntax highlighting: SQL comments (#1354)</li>
<li>Request for updating code: a new language(ABAP) in &quot;syntax highlighting&quot; (PR #1340)</li>
<li>Added &quot;none&quot; diff algorithm</li>
<li>Enable mouse wheel scrolling on the location pane.</li>
<li>Backup files: Misleading error message when file cannot be written (#1326)</li>
</ul>
<h3 id="binary-compare-5">Binary compare</h3>
<ul>
<li>Allow the &quot;Split Vertically&quot; menu item to be unchecked.</li>
</ul>
<h3 id="image-compare-6">Image compare</h3>
<ul>
<li>BugFix: Fix an issue where the pane was split vertically the next time the window was displayed, even though the &quot;Split Vertically&quot; menu item was unchecked.</li>
<li>Make patience and histogram diff algorithm selectable.</li>
</ul>
<h3 id="webpage-compare-9">Webpage compare</h3>
<ul>
<li>BugFix: Fix text disappearing when pressing the &#39;K&#39; key in the address bar</li>
<li>BugFix: Fix an issue where the pane was split vertically the next time the window was displayed, even though the &quot;Split Vertically&quot; menu item was unchecked.</li>
<li>ResourceTree compare: Set the last-modified date and time in resource files</li>
<li>Added Ctrl+L keyboard shortcut</li>
</ul>
<h3 id="folder-compare-14">Folder compare</h3>
<ul>
<li>BugFix: Alt/Shift key highlighting issue not resetting start point from move. (#1335)</li>
<li>BugFix: Refresh Selected Marks Unscanned Folders as Identical (#1349)</li>
<li>BugFix: Make the file name refreshed to detect case changes when running &quot;Refresh Selected&quot;. (PR #1358)</li>
<li>BugFix: Fix an issue where paths with different case are not displayed correctly in the folder column of the folder compare window when comparing three directories. (PR #1372)</li>
<li>Tab behaviour on comparisons of files vs folders (#1367)</li>
<li>Make the sort order of file names including numbers the same as Explorer. (osdn.net #44557)</li>
</ul>
<h3 id="reports-1">Reports</h3>
<ul>
<li>BugFix: Fix report generation process. (PR #1324)</li>
<li>BugFix: Fix report generation process about replacement with HTML entity (PR #1344)</li>
<li>Modify &quot;Tools - Generate Report&quot; (Add column width definition to html output) (PR #1333)</li>
<li>BugFix: Fix an issue where WinMerge crashes depending on the filename when generating a file compare report. (PR #1319)</li>
</ul>
<h3 id="configuration-log">Configuration log</h3>
<ul>
<li>BugFix: [Bug Report] WinMerge does not recognize Win 11 (#1192)</li>
</ul>
<h3 id="plugins-10">Plugins</h3>
<ul>
<li>BugFix: CompareMSExcelFiles Plugins did not compare folders when opening .xlsx files from Plugins-&gt; Edit with Unpacker menu item even though the &quot;Extract workbook data to multiple files&quot; option is enabled in the plugin settings (osdn.net #44522)</li>
<li>BugFix: Fix a problem where the &quot;Open files in the same window type after unpacking&quot; checkbox was checked, but the checkbox was not checked the next time the dialog was opened.</li>
<li>Sugg: Increase the dialogue for Plugins (#1308)</li>
</ul>
<h3 id="command-line-3">Command line</h3>
<ul>
<li>BugFix: Crash on command line compare (#1363)</li>
</ul>
<h3 id="shell-extension-1">Shell extension</h3>
<ul>
<li>BugFix: Fix an issue where the WinMerge menu displayed in the&quot;Show more options&quot; menu of the Windows 11 Explorer context menu is not an advanced menu, even though the advanced menu is enabled.</li>
<li>BugFix: Fix the problem that the WinMerge icon is not correctly displayed on the taskbar when WinMerge is started from the Windows 11 context menu.</li>
</ul>
<h3 id="translations-17">Translations</h3>
<ul>
<li>Translation updates:
<ul>
<li>Chinese Simplified (PR #1330)</li>
<li>Corsican (PR #1331,#1347)</li>
<li>German (PR #1311,#1329)</li>
<li>Hungarian (PR #1325)</li>
<li>Italian (PR #1355)</li>
<li>Japanese (PR #1338)</li>
<li>Lithuanian (PR #1318,#1327)</li>
<li>Polish (#1323)</li>
<li>Portuguese (PR #1317,#1345)</li>
<li>Slovenian</li>
<li>Turkish (#1332)</li>
<li>Russian (PR #1310)</li>
</ul></li>
</ul>
<h3 id="internals-4">Internals</h3>
<ul>
<li>Fix typo in BCMenu.cpp (PR #1313)</li>
<li>Fix type: GPL (PR #1342)</li>
<li>Use isupper+tolower instead of islower+toupper (diffutils ver2.7) (PR #1351)</li>
<li>Initialize variables defined at &quot;diff.h&quot; (PR #1360)</li>
</ul>
<h2 id="winmerge-21620---2022-04-27">WinMerge 2.16.20 - 2022-04-27</h2>
<h3 id="general-11">General</h3>
<ul>
<li>BugFix: New filter (F) display (#1281 a))</li>
</ul>
<h3 id="file-compare-17">File compare</h3>
<ul>
<li>BugFix: Fixed a problem where the caret would not display in the correct position on lines containing tab characters, depending on the font in use (osdn.net #44417)</li>
</ul>
<h3 id="webpage-compare-10">Webpage compare</h3>
<ul>
<li>Webpage Compare [EXPERIMENTAL] (PR #1182)
<ul>
<li>Requires WebView2 Runtime.</li>
<li>Only supported on Windows 10 and above.</li>
<li>Currently, it is not possible to directly highlight differences between web pages, but it is possible to display two or three web pages side by side. You can also compare the following content of the displayed web pages
<ul>
<li>Screenshots</li>
<li>HTML contents</li>
<li>Extracted texts</li>
<li>Resource trees</li>
</ul></li>
</ul></li>
</ul>
<h3 id="folder-compare-15">Folder compare</h3>
<ul>
<li>BugFix: Fix an issue where items with different case are not displayed correctly in the folder compare window when comparing three directories. (PR #1299)</li>
</ul>
<h3 id="options-dialog-8">Options dialog</h3>
<ul>
<li>Allow resizing Options dialog box in both directions (#1265)</li>
</ul>
<h3 id="plugins-11">Plugins</h3>
<ul>
<li>BugFix: CompareMSExcelFiles.sct: Date formats interpreted inconsistently (#279)</li>
<li>Add URL handler plugins (PR #1270)
<ul>
<li>HTTP/HTTPS scheme handler plugin
<ul>
<li>This plugin retrieves a file with the specified HTTP or HTTPS URL using the curl command.</li>
</ul></li>
<li>Windows Registry scheme(reg:) handler plugin
<ul>
<li>This plugin handles URLs like <code>reg:HKEY_CURRENT_USER\Software\Thingamahoochie\WinMerge</code>. After <code>reg:</code>, it considers it a registry key and uses the reg.exe command to retrieve information about that registry key.</li>
</ul></li>
</ul></li>
<li>Replace plugin: When regular expression substitution is performed with the <code>-e</code> option, <code>\r</code> and <code>\n</code> in the second argument are now treated as control characters CR and LF.</li>
<li>PrettifyHTML plugin: Added &quot;--tidy-mark no&quot; to default plugin arguments</li>
</ul>
<h3 id="command-line-4">Command line</h3>
<ul>
<li>Added <code>/t webpage</code> command line option</li>
</ul>
<h3 id="manual-1">Manual</h3>
<ul>
<li>BugFix: Help file: Small issue for plugins (#1309)</li>
</ul>
<h3 id="translations-18">Translations</h3>
<ul>
<li>Translation updates:
<ul>
<li>Corsican (PR #1305,#1307)</li>
<li>Bulgarian (PR #1269)</li>
<li>French (PR #1294)</li>
<li>Galician (PR 1292)</li>
<li>German (PR #1276)</li>
<li>Hungarian (PR #1274,#1306)</li>
<li>Japanese</li>
<li>Lithuanian (PR #1263,#1275)</li>
<li>Polish (PR #1272,#1287,#1288)</li>
<li>Portuguese (PR #1273,#1277)</li>
<li>Slovenian (#1289)</li>
<li>Turkish (PR #1264)</li>
</ul></li>
</ul>
<h3 id="internals-5">Internals</h3>
<ul>
<li>Code refactor with C++17 features replace optimize create smart pointers (PR #1304)</li>
<li>Fixed link errors occurring in Windows 10 SDK version 10.0.19041.0 and lower</li>
</ul>
<h2 id="winmerge-21619---2022-03-20">WinMerge 2.16.19 - 2022-03-20</h2>
<h3 id="general-12">General</h3>
<ul>
<li>Update Merge.rc (PR #1219,#1227,#1231,#1232)</li>
</ul>
<h3 id="file-compare-18">File compare</h3>
<ul>
<li>BugFix: Match similar lines breaks with Ignore whitespace change (#1209)</li>
<li>BugFix: Copy &amp; Advance skips differences when moved block detection is on (#1235)</li>
<li>BugFix: Fix inline difference selection by F4 key not working well in various cases</li>
<li>Different exit procedure required for small vs. large files (#1218)</li>
<li>Added View → View Top Margins menu item. (A ruler appears in the margin)</li>
</ul>
<h3 id="table-compare-4">Table compare</h3>
<ul>
<li>Pinning first row of file (#999)
<ul>
<li>Added Use First Line as Headers menu item to the column header context menu.</li>
</ul></li>
</ul>
<h3 id="folder-compare-16">Folder compare</h3>
<ul>
<li>BugFix: Fix the problem that the status bar displays &quot;0 items selected&quot; even though multiple items are selected.</li>
<li>BugFix: Change the file naming method of the file compare report to avoid duplication of the file compare report file name linked from the folder compare report. (PR #1171)</li>
<li>BugFix: Fix an issue where comparison results are not displayed correctly when &quot;Refresh Selected&quot; is performed by selecting an item that has a directory and file with the same name. (PR #1189)</li>
<li>BugFix: Folder compare with jpg images crashes (#1176)</li>
<li>BugFix: Fix renaming process in folder compare window. (PR #1246)</li>
</ul>
<h3 id="filters-1">Filters</h3>
<ul>
<li>Filters enchancement (PR #1179,#1174)
<ul>
<li>File masks
<ul>
<li>Putting <code>!</code> before file masks will exclude files that match that masks.</li>
<li>If you put <code>\</code> after the file masks, the masks will match folders instead of files.</li>
</ul></li>
<li>File filters
<ul>
<li>Added <code>f!:</code> and <code>d!:</code> to exclude files or folders that match the pattern specified in <code>f:</code> and <code>d:</code>.</li>
</ul></li>
</ul></li>
</ul>
<h3 id="options-dialog-9">Options dialog</h3>
<ul>
<li>BugFix: Help text is truncated (#1210)</li>
<li>Improve vertical alignment string (#1200)</li>
<li>Some improvements (#1212)</li>
</ul>
<h3 id="plugins-12">Plugins</h3>
<ul>
<li>BugFix: Select Plugin Dialog: Fix the problem that the plugin arguments are deleted by clicking the &quot;Add pipe&quot; button after entering them.</li>
</ul>
<h3 id="translations-19">Translations</h3>
<ul>
<li>Translation updates:
<ul>
<li>Catalan (PR #1237)</li>
<li>Chinese Simplified (PR #1257)</li>
<li>Chinese Traditional (PR #1204)</li>
<li>Corsican (PR #1188,#1205,#1221,#1251,#1260)</li>
<li>Dutch (PR #1187)</li>
<li>French (PR #1211)</li>
<li>German (PR #1208,#1228,#1254,#1262)</li>
<li>Hungarian (PR #1203,#1220,#1252,#1259)</li>
<li>Japanese (PR #1165)</li>
<li>Korean (PR #1181)</li>
<li>Lithuanian (PR #1197,#1202,#1224,#1255)</li>
<li>Norwegian (PR #1170)</li>
<li>Portuguese (PR #1178,#1222)</li>
<li>Russian (PR #1164)</li>
<li>Slovak (PR #1196)</li>
<li>Slovenian (PR #1163,#1261)</li>
<li>Ukrainian (PR #1172)</li>
</ul></li>
</ul>
<h3 id="internals-6">Internals</h3>
<ul>
<li>Fix typo in ShellFileOperations.cpp (PR #1256)</li>
<li>[Big PR - big changes] A lot of refactor and optimization commits (PR #1258)</li>
<li>Wrong links for ShellExtension on Translations page (#1185)</li>
<li>Tweak translations status (PR #1201)</li>
</ul>
<h2 id="winmerge-21618---2022-01-27">WinMerge 2.16.18 - 2022-01-27</h2>
<h3 id="general-13">General</h3>
<ul>
<li>BugFix: Crash when comparing files in Google Drive</li>
<li>[Feature Request] Lengthen title of File Compare window or add tip (#960)</li>
<li>added me to contributor list (PR #1094)</li>
<li>Made it so that the parent window cannot be operated while the font selection dialog displayed from View→Select Font menu item is displayed.</li>
</ul>
<h3 id="file-compare-19">File compare</h3>
<ul>
<li>BugFix: wm ********* crashes with file attached (#1101)</li>
<li>BugFix: Fix a problem that &#39;Encountered an improper argument&#39; error occurs when a pane split by Window→Split menu item is unsplit by drag operation.</li>
<li>BugFix: Colors -&gt; Differences -&gt; Word Difference : Text color ignored (#1116)</li>
<li>BugFix: WinMerge crashes with specific regex search (#1160)</li>
<li>3-Way File Merge: No Keyboard / hot keys available for Merging from Left Pane to right pane and vice versa (#957)</li>
<li>Winmerge hangs when i try to compare files. (#1111)</li>
<li>Fast encoding switching. (#793)</li>
</ul>
<h3 id="clipboard-compare">Clipboard Compare</h3>
<ul>
<li>New Feature: Clipboard Compare (PR #1147)
<ul>
<li>Click File → Open Clipboard menu item to compare the two most recent contents of the clipboard history.</li>
<li>You can also compare by pressing Ctrl+V when the child MDI window is not visible.</li>
<li>This feature is available on Windows 10 version 1809 or higher and WinMerge 64-bit version.</li>
</ul></li>
</ul>
<h3 id="folder-compare-17">Folder compare</h3>
<ul>
<li>BugFix: Sorting on Comparison Result being done incorectly (#483)</li>
<li>BugFix: Fix an issue where WinMerge sometimes crashes when executing &quot;Refresh Selected&quot; in the folder compare window. (PR #1120)</li>
<li>BugFix: Fixed a bug that the parent folder icon was not displayed in non-recursive mode.</li>
<li>BugFix: Fixed the problem that the sort order is different from version 2.16.16 or earlier</li>
</ul>
<h3 id="plugins-13">Plugins</h3>
<ul>
<li>Fix for <a href="https://github.com/WinMerge/winmerge/discussions/1139">https://github.com/WinMerge/winmerge/discussions/1139</a> (#1139,PR #1140)</li>
<li>Make plugin descriptions translatable</li>
<li>Upgrade Apache Tika to 2.2.1</li>
</ul>
<h3 id="command-line-5">Command line</h3>
<ul>
<li>Added <code>/clipboard-compare</code> command line option</li>
</ul>
<h3 id="archive-support-4">Archive support</h3>
<ul>
<li>Update 7-Zip to 21.07</li>
</ul>
<h3 id="installer-6">Installer</h3>
<ul>
<li>BugFix: Incorrect link to &quot;Quick Start&quot; guide at WM installation end (#1127)</li>
<li>BugFix: Add replacesameversion flag to 7z.dll</li>
<li>Re-enabled the process of installing ShellExtension for Windows 11.</li>
</ul>
<h3 id="manual-2">Manual</h3>
<ul>
<li>BugFix: &quot;Quick compare limit&quot; and &quot;Binary compare limit&quot; settings don&#39;t have the expected (and documented) purpose (#1100)</li>
</ul>
<h3 id="translations-20">Translations</h3>
<ul>
<li>Translation updates:
<ul>
<li>Chinese Simplified (PR #1109,#1112,#1134)</li>
<li>Corsican (PR #1103,#1119,#1137,#1142,#1154)</li>
<li>Dutch (PR #1123)</li>
<li>French (PR #1121,#1122,#1157)</li>
<li>German (PR #1110,#1117,#1143,#1155)</li>
<li>Hungarian (PR #1102,#1115,#1136,#1141,#1150)</li>
<li>Japanese</li>
<li>Lithuanian (PR #1124,#1144)</li>
<li>Portuguese (PR #1097,#1098,#1106,#1133,#1149)</li>
<li>Slovenian (PR #1148,#1153)</li>
<li>Turkish (PR #1099)</li>
</ul></li>
</ul>
<h3 id="internals-7">Internals</h3>
<ul>
<li>Fix typo in DirScan.cpp (PR #1118)</li>
</ul>
<h2 id="winmerge-21617---2021-12-19">WinMerge 2.16.17 - 2021-12-19</h2>
<h3 id="general-14">General</h3>
<ul>
<li>New Option to ignore numbers. (PR #1025,#923)</li>
<li>Add the feature to display tooltips on the MDI tab. (PR #1038)</li>
<li>Issue with closing WinMerge with Esc keyboard key (#1052)</li>
<li>Add an &quot;Defaults&quot; section to the ini file (PR #1071)</li>
</ul>
<h3 id="file-compare-20">File compare</h3>
<ul>
<li>BugFix: Release 2.16.16 crashes when comparing large files - likely a regression (#1036)</li>
<li>BugFix: Fixed C#(Java, JavaScript) keyword highlighting. (#1040)</li>
<li>BugFix: The current pane switches without me asking it to. (#1050)</li>
<li>BugFix: Fix PHP syntax highlighting. (PR #1055)</li>
<li>BugFix: Source Files Comparison doesn&#39;t seem to Work properly (#1057)</li>
<li>Add D syntax highlighting. (PR #1042)</li>
<li>Improved &#39;Match similar lines&#39; option (#1013)</li>
<li>Make the &#39;Match similar lines&#39; option work for 3-way comparisons (PR #1051,#510)</li>
<li>Please add a huge icon for &quot;Filter is active&quot; (#1056)</li>
</ul>
<h3 id="image-compare-7">Image compare</h3>
<ul>
<li>Added support for creating multi-page image compare report (osdn.net #43374)</li>
</ul>
<h3 id="folder-compare-18">Folder compare</h3>
<ul>
<li>BugFix: Fixed a problem where Duplicate Group Numbers were not assigned to files with the same content but different file names.</li>
<li>BugFix: Fix crash when comparing 3 folders if additional properties were added</li>
<li>FolderCompare: Improve performance when tree mode is disabled (#PR #1069)</li>
</ul>
<h3 id="project-file-3">Project file</h3>
<ul>
<li>Add a feature to save/restore the &quot;Ignore numbers&quot; setting to/from a project file. (PR #1068)</li>
</ul>
<h3 id="patch-generator-dialog-1">Patch Generator dialog</h3>
<ul>
<li>Put the diff patch to the clipboard rather than to files (#923)</li>
</ul>
<h3 id="plugins-14">Plugins</h3>
<ul>
<li>BugFix: Fixed the problem that Plugins-&gt;Reload Plugins menu item does not work.</li>
</ul>
<h3 id="archive-support-5">Archive support</h3>
<ul>
<li>Update 7-Zip to 21.06</li>
</ul>
<h3 id="shell-extension-2">Shell extension</h3>
<ul>
<li>ShellExtension for Windows 11: Implemented advanced menu</li>
</ul>
<h3 id="translations-21">Translations</h3>
<ul>
<li>New translation:
<ul>
<li>Corsican (PR #1072,#1085)</li>
</ul></li>
<li>Translation updates:
<ul>
<li>Chinese Traditional (PR #1079)</li>
<li>Galician (PR #1089)</li>
<li>German (PR #1062,#1086,#1088)</li>
<li>Hungarian (PR #1032)</li>
<li>Japanese</li>
<li>Korean (PR #1078)</li>
<li>Lithuanian (PR #1043,#1061,#1082,#1087)</li>
<li>Polish (PR #1049)</li>
<li>Portuguese (PR #1034,#1039,#1060,#1065)</li>
<li>Russian (PR #1031)</li>
<li>Slovenian</li>
</ul></li>
</ul>
<h3 id="internals-8">Internals</h3>
<ul>
<li>BugFix: Fix typo in BCMenu.cpp (PR #1054)</li>
<li>BugFix: Return better HRESULTs (PR #1077)</li>
<li>Make it buildable for ARM32 architecture</li>
</ul>
<h2 id="winmerge-21616---2021-10-28">WinMerge 2.16.16 - 2021-10-28</h2>
<h3 id="general-15">General</h3>
<ul>
<li>Fix a problem where the string in the Windows common dialog would not change to the language when switching languages.</li>
</ul>
<h3 id="file-compare-21">File compare</h3>
<ul>
<li>BugFix: Fix not getting the proper error message when saving failed</li>
</ul>
<h3 id="table-compare-5">Table compare</h3>
<ul>
<li>BugFix: Cannot resize last column with UI (#998)</li>
<li>Reloading a file that was changed by another application does not preserve column widths (#951)</li>
</ul>
<h3 id="image-compare-8">Image compare</h3>
<ul>
<li>BugFix: Fix an issue where drag-and-drop of file would only work once.</li>
</ul>
<h3 id="folder-compare-19">Folder compare</h3>
<ul>
<li>BugFix: Sync (Super Slow) (#771)</li>
<li>BugFix: Fix an issue where filters are not applied correctly when opening a project file containing multiple items with different filters. (PR #995)</li>
<li>[Feature Request] New Display Columns: Dimensions + Size Difference (#131)</li>
<li>FolderCompare: Additional Properties (Windows Property System+Hash (MD5, SHA-1, SHA-256)) (PR #996)</li>
</ul>
<h3 id="options-dialog-10">Options dialog</h3>
<ul>
<li>BugFix: Fix the problem that the &quot;Register Shell Extension for Windows 11 or later&quot; button is not enabled when another user has registered ShellExtension for Windows 11.</li>
</ul>
<h3 id="plugins-15">Plugins</h3>
<ul>
<li>BugFix: Plugin unpacked file extension problem (get_PluginUnpackedFileExtension) (#983)</li>
<li>BugFix: Comparing broken lnk-files (windows shortcuts) freezes WinMerge (#1007)</li>
<li>Apache Tika plugin: Update Apache tika to 2.1.0 and change the download URL</li>
<li>CompareMSExcelFiles.sct: Make the number before the sheet name zero-padded</li>
</ul>
<h3 id="shell-extension-3">Shell extension</h3>
<ul>
<li>BugFix: ShellExtension for Windows 11 did not work on machines that did not have MSVCP140.dll VCRUNTIME140*.dll installed.</li>
<li>BugFix: Loop counter should be the same type as the count type. (PR #987)</li>
<li>ShellExtension for Windows11: Disable Registry Write Virtualization</li>
</ul>
<h3 id="manual-3">Manual</h3>
<ul>
<li>Where to report documentation/help errors? (#1004)</li>
</ul>
<h3 id="translations-22">Translations</h3>
<ul>
<li>Translation updates:
<ul>
<li>Galician (PR #1005)</li>
<li>German (PR #986,#1027)</li>
<li>Hungarian (PR #991,#1023)</li>
<li>Japanese</li>
<li>Lithuanian (PR #979)</li>
<li>Portuguese (PR #1017)</li>
<li>Slovenian (#1026)</li>
<li>Turkish (PR #980)</li>
</ul></li>
</ul>
<h3 id="internals-9">Internals</h3>
<ul>
<li>BugFix: Missing packages.config (also outdated) and wrong NuGet packages path in the WinMergeContextMenu.vcxproj and .filters file (#985)</li>
<li>Fix typo in OpenView.cpp (PR #1000)</li>
</ul>
<h2 id="winmerge-21615---2021-09-20">WinMerge 2.16.15 - 2021-09-20</h2>
<h3 id="general-16">General</h3>
<ul>
<li>BugFix: WinMerge would crash when launched if the registry or INI file contained invalid values.</li>
<li>BugFix: Winmerge Crash when comparing 2 files from Windows Explorer context menu (#808, #908, #913)</li>
<li>BugFix: Incorrect text color for selected menu item on Windows 11</li>
<li>BugFix: 50% cpu use by winmergeu.exe after program closed (#903)</li>
<li>Digitally sign packages (#152)</li>
</ul>
<h3 id="file-compare-22">File compare</h3>
<ul>
<li>BugFix: The mouse cursor did not change to an hourglass when the files or plugins were taking a long time to load.</li>
<li>BugFix: Save Middle and Save Middle As menu items were not enabled when comparing three files.</li>
<li>BugFix: A two-pane window was displayed even though New (3panes) → Table menu item was selected.</li>
<li>BugFix: The height of each pane in the Diff pane was calculated incorrectly when comparing three files.</li>
<li>BugFix: Unicode SMP chars misrendered after performing a find (#914)</li>
<li>BugFix: Crash when pressing Shift+F4 key</li>
<li>BugFix: Replace slow (#940)</li>
<li>BugFix: When moving in the scroll pane, the selected position is incorrect (#970)</li>
<li>BugFix: When the Diff pane was redisplayed, the scroll position of the Diff pane was not appropriate. (osdn.net #42862)</li>
<li>Make &quot;Do not close this box&quot; checkbox in search window On by default (#941)</li>
</ul>
<h3 id="image-compare-9">Image compare</h3>
<ul>
<li>BugFix: Duplicate menu shortcut in translations (#905)</li>
<li>BugFix: Image comparison (winimerge #24)</li>
</ul>
<h3 id="project-file-4">Project file</h3>
<ul>
<li>Add a feature to save/restore compare options to/from a project file.(#498) (PR #915)</li>
</ul>
<h3 id="options-dialog-11">Options dialog</h3>
<ul>
<li>Add a feature to set items saved to or restored from the project file. (PR #953)</li>
</ul>
<h3 id="plugins-16">Plugins</h3>
<ul>
<li>New unpacker plugins:
<ul>
<li>DecompileJVM</li>
<li>DecompileIL</li>
<li>DisassembleNative</li>
</ul></li>
</ul>
<h3 id="command-line-6">Command line</h3>
<ul>
<li>Added /c <code>column number</code> command line option</li>
<li>Added /EnableExitCode command line option</li>
</ul>
<h3 id="shell-extension-4">Shell extension</h3>
<ul>
<li>BugFix: WinMerge&#39;s extended menu items were doubly inserted into the context menu of Explorer&#39;s navigation pane. (osdn.net #42702)</li>
<li>BugFix: Right click - compare - is unclear (#249)</li>
<li>Added a new DLL (WinMergeContextMenu.dll) for the Windows 11 Explorer context menu (currently unstable and not registered by default) (PR #954)</li>
</ul>
<h3 id="translations-23">Translations</h3>
<ul>
<li>Translation updates:
<ul>
<li>Brazilian (PR #910)</li>
<li>Dutch (PR #921,#969)</li>
<li>German (PR #965,#977)</li>
<li>Hungarian (PR #937,#955)</li>
<li>Italian (PR #911)</li>
<li>Japanese</li>
<li>Korean (PR #932)</li>
<li>Portuguese (PR #956,#964,#976)</li>
<li>Russian (PR #901,#927,#963)</li>
<li>Slovenian</li>
<li>Swedish (PR #974)</li>
<li>Turkish (PR #899)</li>
</ul></li>
</ul>
<h3 id="internals-10">Internals</h3>
<ul>
<li>README.md: Make it clear that requirements are to build, not use the application (PR #942)</li>
<li>compiler-calculated maximum value for <code>m_SourceDefs</code> (PR #966)</li>
</ul>
<h2 id="winmerge-21614---2021-07-25">WinMerge 2.16.14 - 2021-07-25</h2>
<h3 id="general-17">General</h3>
<ul>
<li>Fixed an issue where the WinMerge process might not terminate even though the WinMerge window was closed.</li>
</ul>
<h3 id="file-compare-23">File compare</h3>
<ul>
<li>BugFix: Fixed an infinite loop when &quot;find what&quot; in the substitution filters is empty.</li>
</ul>
<h3 id="folder-compare-20">Folder compare</h3>
<ul>
<li>BugFix: Fix an issue where a file is deselected when returning to the folder compare window after opening the file compare window by double-clicking the file in the folder compare window. (PR #857)</li>
<li>Right click context menu - Compare files or folders in a new tab (#232,#277)</li>
</ul>
<h3 id="binary-compare-6">Binary compare</h3>
<ul>
<li>BugFix: Fixed an issue where window titles may not be updated</li>
</ul>
<h3 id="image-compare-10">Image compare</h3>
<ul>
<li>winmerge shows (differences) rotated image (winmerge/winimerge #20)</li>
<li>Added following menu items to the context menu
<ul>
<li>Rotate Right 90deg</li>
<li>Rotate Left 90deg</li>
<li>Flip Vertically</li>
<li>Flip Horizontally</li>
</ul></li>
</ul>
<h3 id="options-dialog-12">Options dialog</h3>
<ul>
<li>Add preference option to clear &quot;Don&#39;t ask this question again&quot; CompareLargeFiles choice (#772, PR #859)</li>
</ul>
<h3 id="select-files-or-folders-dialog-2">Select Files or Folders dialog</h3>
<ul>
<li>BugFix: Fix the Select Files or Folders dialog. (PR #882,#892)</li>
</ul>
<h3 id="plugins-17">Plugins</h3>
<ul>
<li>BugFix: CompareMSExcelFiles.sct: &quot;This picture only contains a bitmap&quot; was displayed when comparing Excel files that contain shapes.</li>
<li>BugFix: CString rangestr = (argc &gt; 0) ? argv[0] : GetColumnRangeString(); (#853)</li>
<li>Improve plugin system (editor script) (PR #871)</li>
<li>New unpacker plugins:
<ul>
<li>PrettifyHTML</li>
<li>PrettifyYAML</li>
<li>ValidateHTML</li>
<li>QueryYAML</li>
<li>SelectColumns</li>
<li>SelectLines</li>
<li>ReverseColumns</li>
<li>ReverseLines</li>
<li>Replace</li>
</ul></li>
<li>New editor script plugins:
<ul>
<li>PrettifyHTML</li>
<li>PrettifyYAML</li>
<li>SelectColumns</li>
<li>SelectLines</li>
<li>ReverseColumns</li>
<li>ReverseLines</li>
<li>Replace</li>
</ul></li>
<li>Updated Apache Tika to version 2.0.0</li>
<li>Updated yq to version 4.11.1</li>
</ul>
<h3 id="command-line-7">Command line</h3>
<ul>
<li>Added /l command line option (osdn.net #41528)</li>
<li>Added /t, /table-delimiter, /new, /fileext and /inifile command line option</li>
</ul>
<h3 id="installer-7">Installer</h3>
<ul>
<li>Installer integrates with TortoiseGit and TortoiseSVN despite being told not to (#878)</li>
</ul>
<h3 id="translations-24">Translations</h3>
<ul>
<li>Translation updates:
<ul>
<li>Bulgarian (PR #850)</li>
<li>Dutch (PR #842,#893)</li>
<li>Galician (PR #869)</li>
<li>German (PR #860,#870,#883,#890)</li>
<li>Hungarian (PR #845,#856,#897)</li>
<li>Japanese</li>
<li>Lithuanian (PR #840,#849,#866,#875,#879,#894)</li>
<li>Portuguese (PR #846,#872,#898)</li>
<li>Slovenian (#858)</li>
<li>Russian (PR #847)</li>
<li>Turkish (PR #848)</li>
</ul></li>
</ul>
<h3 id="internals-11">Internals</h3>
<ul>
<li>BugFix: WinMerge doesn&#39;t build under Visual Studio 16.10.2 (#841)</li>
<li>BugFix: x64: LINK : warning LNK4010: invalid subsystem version number 5.01; default subsystem version assumed (#855)</li>
<li>BugFix: Project: heksedit cl : command line warning D9002: ignoring unknown option &#39;/arch:SSE&#39; (#861)</li>
<li>BugFix:ALL.vs2019.sln cl : command line warning D9035: option &#39;Gm&#39; has been deprecated and will be removed in a future release (#862)</li>
<li>Resolves: Add continuous code security and quality analysis (PR #844, #843)</li>
<li>Improvement: Add check and error mesage in DownloadDeps.cmd that path to 7-zip exists (#864)</li>
</ul>
<h2 id="winmerge-21613---2021-06-22">WinMerge 2.16.13 - 2021-06-22</h2>
<h3 id="general-18">General</h3>
<ul>
<li>BugFix: Register.bat did not work properly on the Chinese version of Windows XP (#780)</li>
<li>Possibility to store settings in INI file (#248) (PR #750)</li>
<li>FeatureRequest - Ignoring options - lack in &quot;button menu&quot; (#804)</li>
</ul>
<h3 id="file-compare-24">File compare</h3>
<ul>
<li>BugFix: Fix PHP syntax highlighting. (PR #782, PR #802)</li>
<li>BugFix: BS key did not work at the beginning of the line after splitting the pane or clicking the OK button in the Options dialog.</li>
<li>BugFix: The edited contents were discarded when the &quot;Recompare As&quot; menu item was selected after editing the file</li>
<li>BugFix: Incorrect comparison (#834) (This bug occured when enabling both the &#39;Ignore blank lines&#39; option and the &#39;Completely unhighlight the ignored differences&#39; option.)</li>
<li>Add Smarty syntax highlighting. (PR #821)</li>
<li>Thicken the caret in overwrite mode (osdn.net #42179)</li>
</ul>
<h3 id="folder-compare-21">Folder compare</h3>
<ul>
<li>BugFix: Different Files are Identical? (#768) (When comparing files with only BOM and no contents, the comparison result became unstable because it referred to the uninitialized memory.)</li>
<li>BugFix: Fix a crash when re-comparing folders (osdn.net #42219)</li>
</ul>
<h3 id="binary-compare-7">Binary compare</h3>
<ul>
<li>BugFix: The file could not be saved after creating a new one</li>
<li>Made Unpacker plugins available for image compare and binary compare</li>
</ul>
<h3 id="image-compare-11">Image compare</h3>
<ul>
<li>BugFix: Error on try to show differences between two different gif (#784)</li>
<li>Made Unpacker plugins available for image compare and binary compare</li>
</ul>
<h3 id="plugins-18">Plugins</h3>
<ul>
<li>Improve plugin system (PR #797)
<ul>
<li>Added categorized Unpacker plugin menu items to various menus</li>
<li>Made it possible to specify multiple Unpacker/Prediffer plugins by concatenating them with | and to specify arguments.</li>
<li>Allowed Unpacker/Prediffer plugins to be specified for multiple files at once in Folder Compare window. The specified plugin will be visible in the Unpacker and Prediffer columns</li>
<li>Add &quot;(U)&quot; or &quot;(P)&quot; to the tab title of the window opened by the Unpacker/Prediffer plugin.</li>
<li>Allowed the plugin settings dialog to specify default arguments that will be used when no plugin arguments are specified</li>
<li>Allowed plugins used by Automatic Unpacking/Prediffing to be excluded in the plugin settings dialog</li>
<li>New unpacker plugins:
<ul>
<li>ApacheTika</li>
<li>PrettifyJSON</li>
<li>PrettifyXML</li>
<li>QueryCSV</li>
<li>QueryTSV</li>
<li>QueryJSON</li>
<li>MakeUpper</li>
<li>MakeLower</li>
<li>RemoveDuplicates</li>
<li>CountDuplicates</li>
<li>SortAscending</li>
<li>SortDescending</li>
<li>ExecFilterCommand</li>
<li>Tokenize</li>
<li>Trim</li>
<li>VisualizeGraphviz</li>
</ul></li>
<li>New editor script plugins:
<ul>
<li>RemoveDuplicates</li>
<li>CountDuplicates</li>
<li>Tokenize</li>
<li>Trim</li>
<li>ApacheTika</li>
<li>PrettifyJSON</li>
<li>PrettifyXML</li>
</ul></li>
</ul></li>
</ul>
<h3 id="translations-25">Translations</h3>
<ul>
<li>BugFix: Fix an issue where a message is not translated.(PR #763)</li>
<li>Translation updates:
<ul>
<li>French (PR #762)</li>
<li>Japanese</li>
<li>Polish (PR #769)</li>
<li>Turkish (PR #803)</li>
<li>Russian (PR #761)</li>
</ul></li>
</ul>
<h3 id="internals-12">Internals</h3>
<ul>
<li>Update CWindowsManagerDialog (PR #811)</li>
<li>Update CWindowsManagerDialog - check some pointers for null and made safe casts (PR #824)</li>
</ul>
<h2 id="winmerge-21612---2021-04-29">WinMerge 2.16.12 - 2021-04-29</h2>
<h3 id="general-19">General</h3>
<ul>
<li>GUI textstrings: grammatical corrections (PR #722)</li>
<li>Added ARM64 support</li>
</ul>
<h3 id="file-compare-25">File compare</h3>
<ul>
<li>BugFix: Fix PHP syntax highlighting. (PR #751)</li>
<li>BugFix: Strings in a multi-line diff block were not be replaced correctly when the substitution filters&#39; regular expression contained ^</li>
<li>BugFix: Font size restoration with Ctrl+0 key did not work properly in Hi-DPI environment</li>
<li>BugFix: Fixed a rare crash when enabling syntax highlight</li>
<li>BugFix: Fixed an issue where inline scripts in HTML files were not highlighted correctly</li>
<li>Make the width of the &#39;Find what&#39; label in Find dialog a little wider (osdn.net #42063)</li>
<li>Fix conflicting accelerators (osdn.net #42064)</li>
<li>[Feature Request] Selection Count on Status Bar (#135)</li>
<li>Add C# 6.0 reserved words to style list (PR #719)</li>
<li>FR: Add a shortcut key go to next file (#721)</li>
<li>Update PHP syntax highlighting keyword list. (PR #724)</li>
<li>autoit.cpp - Macros &gt;&gt; User 1 ..... Variable &gt;&gt; User 2 (PR #749)</li>
<li>autoit.cpp - #CS #CE support (PR #753)</li>
<li>Interrupt a inline diff process when it takes too long</li>
<li>Make the color of characters that represent spaces and tabs lighter than other characters when the &quot;View Whitespace&quot; menu item is enabled</li>
<li>Added &quot;Open Parent Folder&quot; menu item to the context menu</li>
</ul>
<h3 id="folder-compare-22">Folder compare</h3>
<ul>
<li>BugFix: Copy confirmation dialog has overlapped Yes/No Button (#739)</li>
<li>BugFix: Comparison result was not updated by Swap operation in 3-way folder comparison (osdn.net #41901)</li>
</ul>
<h3 id="binary-compare-8">Binary compare</h3>
<ul>
<li>BugFix: Next File or Previous File button on the toolbar did not work if the active window was a binary compare window or an image compare window.</li>
<li>BugFix: Only one pane was zoomed in/out in Ctrl+Mouse Wheel</li>
</ul>
<h3 id="image-compare-12">Image compare</h3>
<ul>
<li>BugFix: Next File or Previous File button on the toolbar did not work if the active window was a binary compare window or an image compare window.</li>
<li>BugFix: The header bar widths were not calculated properly when the &#39;Split Vertically&#39; menu item is unchecked.</li>
</ul>
<h3 id="open-dialog">Open dialog</h3>
<ul>
<li>Make archive files openable as binary files from the pull-down menu.</li>
</ul>
<h3 id="archive-support-6">Archive support</h3>
<ul>
<li>BugFix: Fixed a rare crash when decompressing an archive file</li>
</ul>
<h3 id="plugins-19">Plugins</h3>
<ul>
<li>BugFix: ATL: QIThunk - 2 LEAK in plugins e.g. DisplayBinaryFiles and DisplayXMLFiles (#755)</li>
</ul>
<h3 id="translations-26">Translations</h3>
<ul>
<li>Translation updates:
<ul>
<li>Brazilian (PR #711,#728,#729)</li>
<li>Dutch (PR #706)</li>
<li>Galician (PR #737)</li>
<li>German (PR #718,#752)</li>
<li>Hungarian (PR #712)</li>
<li>Japanese</li>
<li>Korean (PR #709)</li>
<li>Lithuanian (PR #708,#713,#738,#754)</li>
<li>Portuguese (PR #725)</li>
<li>Slovenian</li>
<li>Swedish (PR #720,#723)</li>
<li>Russian (PR #707)</li>
</ul></li>
</ul>
<h2 id="winmerge-21611---2021-03-28">WinMerge 2.16.11 - 2021-03-28</h2>
<h3 id="general-20">General</h3>
<ul>
<li>Make all OK strings same case (PR #593)</li>
<li>Tab bar: Added shadows to help distinguish between active and inactive tabs</li>
<li>Added drop-down menu to Open and Save icon on toolbar</li>
</ul>
<h3 id="file-compare-26">File compare</h3>
<ul>
<li>BugFix: Basic syntax highlighter is broken (osdn.net #41440)</li>
<li>BugFix: File is corrupted while saving differences in changed file (Ctrl+S) (#607)</li>
<li>BugFix: Fix an issue where the syntax highlighting scheme is not applied to the split second File pane and the Diff pane when changing it. (PR #624)</li>
<li>BugFix: The position selected by clicking while holding down the shift key is incorrect. (sf.net #2237)</li>
<li>BugFix: Fix an issue where the comment filter might not work properly if the file is non-UTF-8 and the comment contains non-ascii characters.</li>
<li>Add a feature to set up default highlighting by a file extension (PR #627)</li>
<li>Add &quot;Copy Selected Line(s) to/from Left/Right&quot; feature (#463) (PR #642)</li>
<li>If -b or -w is also specified, -B now considers lines to be empty if they contain only white space (osdn.net #41355)</li>
<li>Added BOM checkbox to the Codepage dialog.</li>
</ul>
<h3 id="folder-compare-23">Folder compare</h3>
<ul>
<li>BugFix: Program crash if you close a tab with the folder from where you opened current file (#645)</li>
<li>BugFix: The title bar path was not updated when swapping files in a Zip file.</li>
<li>BugFix: The codepage specified in the Codepage dialog did not affect the files to be opened.</li>
</ul>
<h3 id="image-compare-13">Image compare</h3>
<ul>
<li>BugFix: The image on the right does not open if the file exists only on the right (osdn.net #41721)</li>
</ul>
<h3 id="open-dialog-1">Open dialog</h3>
<ul>
<li>Added Text, Table, Binary and Image menu items to the Compare button in the &quot;Select Files or Folders&quot; window</li>
</ul>
<h3 id="archive-support-7">Archive support</h3>
<ul>
<li>Rar5 support (#644)</li>
</ul>
<h3 id="options-dialog-13">Options dialog</h3>
<ul>
<li>BugFix: Fix an issue where custom colors are not saved. (PR #648)</li>
</ul>
<h3 id="plugins-20">Plugins</h3>
<ul>
<li>RCLocalizationHelper: Fix memory leaks (PR #596)</li>
</ul>
<h3 id="installer-8">Installer</h3>
<ul>
<li>Installer issue with Polish diacritics characters (#589)</li>
</ul>
<h3 id="translations-27">Translations</h3>
<ul>
<li>Translation updates:
<ul>
<li>Bulgarian (PR #599,#625)</li>
<li>Brazilian (PR #633)</li>
<li>French (PR #637,#649,#658,#659)</li>
<li>Galician (PR #587,#650,#677)</li>
<li>German (PR #632,#651)</li>
<li>Hungarian (PR #682,#683,#685)</li>
<li>Japanese</li>
<li>Lithuanian (PR #634,#653)</li>
<li>Polish (PR #597,#673)</li>
<li>Portuguese (PR #674)</li>
<li>Slovenian</li>
<li>Russian (PR #588)</li>
</ul></li>
</ul>
<h3 id="command-line-8">Command line</h3>
<ul>
<li>Single instance mode does not work when launched from Visual Studio 2019 (#622) (Added /sw command line option)</li>
</ul>
<h3 id="internals-13">Internals</h3>
<ul>
<li>BugFix: Plugins\src_VCPP\VCPPPlugins.vs2017.sln can&#39;t open projects any more because in revision 69455da the projects were renamed. (#598)</li>
<li>BugFix: OutputFile of plugin project DisplayXMLFiles is different that the other projects (#600)</li>
<li>BugFix: WinMergeScript.rgs files in Plugins\src_VCPP projects are not found. (#605)</li>
<li>BugFix: typeinfoex.h(189): warning C4701: potentially uninitialized local variable &#39;pTypeLib&#39; used (#605)</li>
<li>BugFix: Plugins project DisplayXMLFiles.vcxproj contains &quot;Unicode Release MinSize&quot; configuration but was removed (#611)</li>
<li>Expression is always true (#612,PR #613)</li>
<li>Plugins.cpp function SearchScriptForMethodName can be improved (#690)</li>
</ul>
<h2 id="winmerge-21610---2021-01-30">WinMerge 2.16.10 - 2021-01-30</h2>
<h3 id="general-21">General</h3>
<ul>
<li>BugFix: Fixed processing that uses GetAsyncKeyState(). (GitHub PR #505)</li>
<li>BugFix: Fixed the problem that the language setting is not applied to the context menu of the MDI tab when the language setting is changed. (GitHub PR #523)</li>
<li>BugFix: command-line option /x did not work for binary and image comparison</li>
<li>BugFix: Substiturions vs. Substitutions (GitHub #551)</li>
<li>Swap Panes functionality for 3 way compares (Issue #507) (GitHub PR #533)</li>
<li>[feature request] Add options to command line to change &quot;compare methods&quot;<br />
(GitHub #530)</li>
<li>Added the command line option &quot;/s-&quot; to ensure that another instance is always executed, ignoring the value of the &#39;Allow only one instance to run&#39; option.</li>
</ul>
<h3 id="file-compare-27">File compare</h3>
<ul>
<li>BugFix: WinMerge crashes with mouse double click (GitHub #531)</li>
<li>BugFix: Fixed an issue where the message box &quot;The report has been created successfully.&quot; was displayed even if the report creation failed.</li>
<li>BugFix: BUG: Doesn&#39;t respect &quot;Don&#39;t ask this question again&quot; checkbox when comparing large files (GitHub #574)</li>
<li>The Location pane doesn&#39;t draw a line connecting moved differences by default (GitHub #498)</li>
<li>Update crystallineparser.cpp - AutoIt (*.au3) support as a &quot;Basic&quot; (GitHub PR #543)</li>
<li>s_apszAutoItKeywordList[] - actualization for AutoIt (GitHub PR #584)</li>
<li>Added JavaScript and JSON syntax highligher (osdn.net #41083)</li>
<li>Added View -&gt; Diff Context -&gt; Invert menu item</li>
<li>Feature Request: Move To Next File option while comparing files #475 (GitHub PR #561)</li>
<li>A new feature &quot;Ignored Substutions&quot; (GitHub PR #544,#549,#560) (&quot;Ignored Substitutions&quot; was renamed to &quot;Substitution Filters&quot;)</li>
</ul>
<h3 id="folder-compare-24">Folder compare</h3>
<ul>
<li>BugFix: Winmerge crashes consistently when deleting files (GitHub #491)</li>
<li>BugFix: Copy Folder does not copy subfolders and I don&#39;t see any option for it (GitHub #537)</li>
</ul>
<h3 id="table-compare-6">Table compare</h3>
<ul>
<li>Added File -&gt; New -&gt; Table menu item</li>
</ul>
<h3 id="binary-compare-9">Binary compare</h3>
<ul>
<li>Added File -&gt; New -&gt; Binary menu item</li>
</ul>
<h3 id="image-compare-14">Image compare</h3>
<ul>
<li>Implement copy and paste images (GitHub PR #524)</li>
<li>Added File -&gt; New -&gt; Image menu item</li>
<li>Added Image -&gt; Compare Extracted Text From Image menu item</li>
</ul>
<h3 id="options-dialog-14">Options dialog</h3>
<ul>
<li>Fix an issue where custom colors are not saved. (GitHub PR #648)</li>
</ul>
<h3 id="filter-dialog">Filter dialog</h3>
<ul>
<li>Making CPropertySheet resizable needs some re-work. (Issue #509) (GitHub PR #535)</li>
</ul>
<h3 id="patch-generator-dialog-2">Patch Generator dialog</h3>
<ul>
<li>BugFix: Crash when generating patch (GitHub #521)</li>
</ul>
<h3 id="translations-28">Translations</h3>
<ul>
<li>Translation updates:
<ul>
<li>Dutch (GitHub #578,#579,#580)</li>
<li>Galician (GitHub PR #493,#538,#570)</li>
<li>German (GitHub PR #532,#548,#563,#585)</li>
<li>Japanese</li>
<li>Lithuanian (GitHub PR #496,#528,#536,#562,#564)</li>
<li>Portuguese (GitHub PR #495)</li>
<li>Slovenian (GitHub #553,#565)</li>
<li>Russian (GitHub PR #494)</li>
</ul></li>
</ul>
<h3 id="manual-4">Manual</h3>
<ul>
<li>Update manual (GitHub PR #497,#513,#546)</li>
<li>Small tweaks for the Manual (GitHub PR #508)</li>
<li>Windows XP Pro SP0 vs Requirements (GitHub #515)</li>
</ul>
<h3 id="internals-14">Internals</h3>
<ul>
<li>Fix Various Warnings (GitHub PR #539)</li>
<li>Various fixes to testing (GitHub PR #545)</li>
<li>Some tweaks for translations status (GitHub PR #556)</li>
<li>Link error when <code>building Plugins\src_VCPP\DisplayXMLFiles</code> with Visual Studio 2019 16.7.7 (GitHub #554)</li>
<li>Link error when building VCPP plugin DisplayXMLFiles with Visual Studio 2019 16.7.7 (GitHub #555)</li>
<li>Link warnings when building VCPP plugin DisplayXMLFiles with Visual Studio 2019 16.7.7 (GitHub #558)</li>
<li>Some more files should be added to the .gitignore file (GitHub #559)</li>
</ul>
<h2 id="winmerge-2169---2020-11-29">WinMerge 2.16.9 - 2020-11-29</h2>
<h3 id="general-22">General</h3>
<ul>
<li>BugFix: MainFrm.cpp: Toolbar was leaking image lists (GitHub PR #432)</li>
<li>BugFix: The icons on a 43&quot; 4K screen are too small (GitHub #276)</li>
<li>Update English.pot (GitHub #440)</li>
<li>Update Merge.rc (GitHub #487)</li>
<li>Improved startup time</li>
</ul>
<h3 id="file-compare-28">File compare</h3>
<ul>
<li>BugFix: [UI] Pane enlargement was reset after changing tab (GitHub #403)</li>
<li>BugFix: Non-comment differences were sometimes ignored when the comment filter was enabled, (osdn.net #40488)</li>
<li>BugFix: Line Filters aren&#39;t applied when using a 3rd file (GitHub #395)</li>
<li>BugFix: &quot;Ignore blank lines&quot; does not work in 3-way compare (GitHub #450)</li>
<li>BugFix: Fix the problem that the comparison result is displayed one line off in the file compare window if a sync point is added with the first line selected. (GitHub PR #435)</li>
<li>BugFix: Fix the problem accessing the text buffer out of range if a sync point is added by selecting a ghost line that is after the last block. (GitHub PR #457)</li>
<li>BugFix: Fix the problem that &quot;Goto...&quot;, &quot;Goto Line &lt;line&gt;&quot; and moving by left click on the location pane do not work appropriately when the Diff pane is active in the file compare window. (GitHub PR #476)</li>
<li>BugFix: Reloading file does not refresh its encoding (GitHub #466)</li>
<li>BugFix: Editor text display is abnormal - character spacing problem (GitHub #468)</li>
<li>always copy &quot;full line&quot; instead of &quot;selected text only&quot; - option CopyFullLine (GitHub PR #459)</li>
<li>Add the feature &quot;Go to Moved Line&quot; requested by #278 (GitHub PR #484)</li>
<li>how to show white space with linebreak hidden? (GitHub #265) (Added View-&gt;View EOL menu item)</li>
</ul>
<h3 id="folder-compare-25">Folder compare</h3>
<ul>
<li>BugFix: Pausing comparing doesn&#39;t pause immediately (GitHub #342)</li>
<li>BugFix: Sorting on Comparison Result being done incorectly (GitHub #483)</li>
<li>Commandline to display versions in the output report (GitHub #418)</li>
<li>&quot;Don&#39;t ask again&quot; checkbox for the Confirm Copy dialog (GitHub PR #445)</li>
</ul>
<h3 id="binary-compare-10">Binary compare</h3>
<ul>
<li>BugFix: Scrolling binary files (GitHub #456)</li>
</ul>
<h3 id="options-dialog-15">Options dialog</h3>
<ul>
<li>Added &quot;Automatically scroll to first inline difference&quot; option to Options dialog</li>
</ul>
<h3 id="patch-generator-dialog-3">Patch Generator dialog</h3>
<ul>
<li>BugFix: Fixed the problem that the input to File1 or File2 comboboxes of the Patch Generator dialog is not applied when the Patch Generator dialog is opened by selecting multiple files. (GitHub PR #421)</li>
</ul>
<h3 id="translations-29">Translations</h3>
<ul>
<li>Translation updates:
<ul>
<li>Chinese Simplified (GitHub PR #465)</li>
<li>Dutch (GitHub PR #482)</li>
<li>Galician (GitHub PR #419,#458,#493)</li>
<li>German (GitHub PR #438,#448,#480,#490)</li>
<li>Lithuanian (GitHub PR #417,#439,#443,#449,#481)</li>
<li>Polish (GitHub PR #447)</li>
<li>Portuguese (GitHub PR #429,#467)</li>
<li>Slovak (GitHub PR #470)</li>
<li>Slovenian (GitHub PR #486,#488)</li>
<li>Spanish (GitHub PR #454)</li>
<li>Turkish (GitHub PR #425)</li>
</ul></li>
</ul>
<h3 id="manual-5">Manual</h3>
<ul>
<li>Update Shortcut_keys.xml (GitHub PR #430)</li>
<li>Update manual (GitHub PR #485,#492)</li>
</ul>
<h3 id="internals-15">Internals</h3>
<ul>
<li>Tweak output from BuildInstaller.cmd and BuildArc.cmd (GitHub PR #424)</li>
<li>Fix typo in GhostTextBuffer.cpp (GitHub PR #472)</li>
<li>Fix typo in memdc.h (GitHub PR #474)</li>
</ul>
<h2 id="winmerge-2168---2020-08-28">WinMerge 2.16.8 - 2020-08-28</h2>
<h3 id="general-23">General</h3>
<ul>
<li>BugFix: More space for some internationalized strings (GitHub #402)</li>
<li>BugFix: Some improvements (GitHub #405,#411)</li>
</ul>
<h3 id="file-compare-29">File compare</h3>
<ul>
<li>BugFix: Ignore case option did not work when Diff algorithm was other than default</li>
<li>BugFix: A white vertical rectangle was sometimes drawn in the selected area</li>
<li>BugFix: The title of the file comparison window after changing the language was accidentally changed to &quot;Untitled left/right&quot;</li>
<li>BugFix: Wrong merging (GitHub #420) (minimal/patience/histogram diff algorithm did not work on Windows XP)</li>
<li>Extended F4 key movement range to the whole file</li>
<li>Don&#39;t treat UTF-8 and UTF-8 with BOM the same when the &quot;Ignore codepage differences&quot; option is disabled</li>
</ul>
<h3 id="folder-compare-26">Folder compare</h3>
<ul>
<li>BugFix: Appropriate error messages were not displayed when the file to be deleted no longer existed</li>
<li>BugFix: &#39;Show Middle/Right Unique items&#39; menu item does not work properly in 3-way folder compare (osdn.net #40672)</li>
<li>CTRL+Drag folders now has the opposite behavior of the current Include Subfolders option</li>
</ul>
<h3 id="image-compare-15">Image compare</h3>
<ul>
<li>BugFix: Fix scrolling glitches (GitHub WinMerge/winimerge PR #8)</li>
<li>Reduce flicker on resize (GitHub WinMerge/winimerge PR #9)</li>
</ul>
<h3 id="options-dialog-16">Options dialog</h3>
<ul>
<li>Allow choosing image filename patterns from a multi-selection dropdown list (GitHub PR #391)</li>
<li>WildcardDropList: Avoid the String instance as it could throw std::bad_alloc (GitHub PR #397)</li>
<li>Remove duplicate filename patterns without relying on WildcardDropList (GitHub PR #400)</li>
<li>Made Options dialog resizable</li>
<li>Changed the default values for the following options:
<ul>
<li>Ignore codepage differences -&gt; disabled</li>
<li>Include unique subfolders contents -&gt; enabled</li>
</ul></li>
</ul>
<h3 id="about-dialog">About dialog</h3>
<ul>
<li>Rework the fix for Github issue #316: GUI glitches/bugs #2 (GitHub PR #392)</li>
<li>Replace outdated list of developers in AboutBox with ascii-art gnu from FSF (GitHub PR #394)</li>
</ul>
<h3 id="installer-9">Installer</h3>
<ul>
<li>BugFix: Installation - Internal error: Failed to expand shell folder constant &quot;userdesktop&quot; (GitHub #354)</li>
<li>BugFix: Lithuanian.po is missing (GitHub PR #415)</li>
<li>New installer for per-user installation (WinMerge-2.16.8-x64-PerUser-Setup.exe)</li>
</ul>
<h3 id="translations-30">Translations</h3>
<ul>
<li>Translation updates:
<ul>
<li>Brazilian (GitHub PR #383)</li>
<li>Galician (GitHub PR #393)</li>
<li>German (GitHub PR #388,#389,#398,#399,#401,#406,#412,#414, WinMerge/frhed PR #2)</li>
<li>Lithuanian (GitHub PR #385,#390,#407,#408,#413,#415)</li>
<li>Turkish (GitHub PR #386)</li>
<li>Russian (GitHub PR #387)</li>
</ul></li>
</ul>
<h3 id="manual-6">Manual</h3>
<ul>
<li>Update manual for IgnoreCommentsC change (GitHub PR #384)</li>
<li>Update Shortcut_keys.xml (GitHub PR #410)</li>
</ul>
<h2 id="winmerge-2167---2020-07-26">WinMerge 2.16.7 - 2020-07-26</h2>
<h3 id="general-24">General</h3>
<ul>
<li>BugFix: The icons on a 43&quot; 4K screen are too small (GitHub #276)</li>
<li>BugFix: GUI glitches/bugs (GitHub #316)</li>
<li>BugFix: Several issues regarding i18n (GitHub #314)</li>
<li>Add Solarized Dark/Light color scheme (GitHub #287)</li>
<li>Compile WinMerge with ASLR and CFG enabled (GitHub #315)</li>
</ul>
<h3 id="file-compare-30">File compare</h3>
<ul>
<li>BugFix: Diff Pane issues (GitHub #307)</li>
<li>BugFix: Codepage not updated on refresh (GitHub #320)</li>
<li>BugFix: Crashes when comparing large files (GitHub #325)</li>
<li>BugFix: WinMerge does not recognize the same files (GitHub #332)</li>
<li>BugFix: Moved ghost lines are shown in the wrong color (GitHub #358)</li>
<li>BugFix: opening same file (GitHub #362)</li>
<li>BugFix: Find dialog sometimes remembers old text to start a new find instead of the new text. (GitHub #368)</li>
<li>BugFix: Fix a problem where markers that are out of the range of the current difference appear in the difference pane. (osdn.net 40407)</li>
<li>BugFix: HTML5 encoding (&lt;meta charset=&quot;...&quot; &gt;) is ignored. (osdn.net #40476)</li>
<li>&#39;=&#39; should be included in word separator (osdn.net #40224)</li>
<li>Allow specifying default for EOL warning checkbox (GitHub #297)</li>
<li>Only indent existing lines (GitHub #356)</li>
</ul>
<h3 id="table-compare-7">Table compare</h3>
<ul>
<li>Made it possible to display the contents of CSV and TSV files like spreadsheet software.</li>
</ul>
<h3 id="folder-compare-27">Folder compare</h3>
<ul>
<li>BugFix: DST causes incorrect dates shown in Date column (GitHub #299)</li>
<li>BugFix: Long filename issue (GitHub #339)</li>
<li>BugFix: Winmerge Freeze if large number of files are listed in Window View (GitHub #348)</li>
<li>BugFix: Change to disable some menu items that should not be executed for directories, when directories are selected in the folder compare window. (GitHub PR #366)</li>
<li>BugFix: Update DirViewColItems.cpp (GitHub #376)</li>
<li>BugFix: Fix the problem that not 3 panes but 2 panes file compare window displays when executing &quot;Compare As&quot; &gt; &quot;Binary&quot; or &quot;Compare As&quot; &gt; &quot;Image&quot; in the context menu in the folder compare window with selecting 3 items. (GitHub PR #381)</li>
<li>BugFix: Fix a problem where a text file could be considered a binary file. (osdn.net #40296)</li>
<li>BugFix: crash when clicking [Merge]-&gt; [Delete] menu item while renaming a file</li>
<li>Add a feature generating a patch from directories (#283) (GitHub PR #331)</li>
<li>added bin and obj directories to C# filter (GitHub PR #365)</li>
</ul>
<h3 id="image-compare-16">Image compare</h3>
<ul>
<li>BugFix: Scrolling behavior when dragging images is wrong (osdn.net #40205)</li>
</ul>
<h3 id="open-dialog-2">Open dialog</h3>
<ul>
<li>BugFix: Open dialogs are sometimes left as garbage (osdn.net #40487)</li>
<li>Browse button in the file/dir selection show wrong path (GitHub #346)</li>
</ul>
<h3 id="options-dialog-17">Options dialog</h3>
<ul>
<li>BugFix: Pressing the [Compare/Binary] category button in the Options dialog twice will cause a crash. (osdn.net #40308)</li>
</ul>
<h3 id="plugins-21">Plugins</h3>
<ul>
<li>BugFix: Fix handling of line breaks in SortAscending, SortDescending (osdn.net PR #40266)</li>
<li>BugFix: Error when comparing images in the CompareMSExcelFiles.sct plugin (osdn.net #40472)</li>
<li>CompareMSExcelFiles.sct: Include Excel Addin files (*.xlam) (GitHub PR #269)</li>
<li>Add support for C# and TypeScript (GitHub PR #382)</li>
<li>Add a plugin for Visio (osdn.net PR #40473)</li>
<li>Plugin fixes for the new Table Compare (CompareMSExcelFiles.sct, IgnoreFieldsComma.dll, IgnoreFieldsTab.dll)</li>
</ul>
<h3 id="archive-support-8">Archive support</h3>
<ul>
<li>BugFix: Crash when comparing encrypted 7z files (GitHub #367)</li>
</ul>
<h3 id="installer-10">Installer</h3>
<ul>
<li>Create the installer with Inno Setup 5.x since installers created with Inno Setup 6.0.x are identified as malware by multiple virus scanning engines</li>
</ul>
<h3 id="translations-31">Translations</h3>
<ul>
<li>Translation updates:
<ul>
<li>Brazilian (GitHub #360)</li>
<li>Chinese Simplified (GitHub PR #303,#377)</li>
<li>Galician (GitHub PR #292,#293,#308,#313,#326)</li>
<li>German (GitHub PR #312,#357)</li>
<li>Lithuanian (GitHub PR #291,#298,#333)</li>
<li>Polish (GitHub PR #280)</li>
<li>Portuguese (GitHub PR #305)</li>
<li>Russian (GitHub PR #270,#271,#272,#302,#327,#328)</li>
<li>Slovak (GitHub PR #300)</li>
<li>Slovenian (GitHub #285)</li>
<li>Spanish (GitHub PR #292,#293,#304,#308)</li>
<li>Turkish (GitHub PR #335,#336,#337,#338)</li>
</ul></li>
</ul>
<h3 id="manual-7">Manual</h3>
<ul>
<li>Minor changes to translations README.md file (GitHub #289)</li>
<li>Update winmerge.org URL to HTTPS, many small improvements (GitHub PR #306)</li>
</ul>
<h3 id="internals-16">Internals</h3>
<ul>
<li>BugFix: Src\Common\MyCom.h unused? (GitHub #284)</li>
<li>BugFix: Error on git repository cloning (GitHub #288)</li>
</ul>
<h2 id="winmerge-2166---2020-02-23">WinMerge 2.16.6 - 2020-02-23</h2>
<h3 id="general-25">General</h3>
<ul>
<li>Added CWindowsManagerDialog class for handling open tabs with Ctrl+Tab, now the application is behave just like professional editors (Visual Studio, Notepad++, etc.) to switch and activate the open tabs. (GitHub #247)</li>
</ul>
<h3 id="file-compare-31">File compare</h3>
<ul>
<li>BugFix: GhostTextBuffer: Don&#39;t unexpectedly bring back empty lines user wants to delete (GitHub #244)</li>
<li>BugFix: Prevent silent abort with File Comparison of files whose size is an exact multiple of 2^32 bytes (GitHub #257)</li>
<li>BugFix: WinMerge ******** crashes after de-selecting Split Vertically (GitHub #259)</li>
<li>BugFix: &quot;Replace All&quot; may not replace all text</li>
<li>BugFix: An error message is shown when pressing Enter key in Diff pane (osdn.net #39924)</li>
<li>BugFix: Non-moved lines are treated as moved lines (osdn.net #39851)</li>
<li>Add support for touchpad horizontal scroll (GitHub #254)</li>
</ul>
<h3 id="image-compare-17">Image compare</h3>
<ul>
<li>BugFix: Support 16-bit images (GitHub WinMerge/winimerge issue #6)</li>
<li>Add support for SVG, PDF, WMF and EMF file types (Only available in WinMerge 64bit version on Windows 10)<br />
Demo1: <a href="https://gyazo.com/b605edb820bc52d0f4f6232eb8ad78aa">https://gyazo.com/b605edb820bc52d0f4f6232eb8ad78aa</a><br />
Demo2: <a href="https://gyazo.com/f5f267546db27f2dc801c00df8cb4251">https://gyazo.com/f5f267546db27f2dc801c00df8cb4251</a></li>
</ul>
<h3 id="archive-support-9">Archive support</h3>
<ul>
<li>BugFix: Third tgz doesn&#39;t get fully extracted (first two do) (GitHub #263)</li>
</ul>
<h3 id="patch-generator-dialog-4">Patch generator dialog</h3>
<ul>
<li>BugFix: Crash when generating patch for multiple files and a file only on one side (GitHub #252)</li>
</ul>
<h3 id="installer-11">Installer</h3>
<ul>
<li>BugFix: Fix spelling of Git (GitHub #246)</li>
</ul>
<h3 id="translations-32">Translations</h3>
<ul>
<li>New translation: Arabic (sf.net #3038)</li>
<li>Translation updates:
<ul>
<li>Bulgarian (GitHub #236, #268)</li>
<li>French (GitHub #251)</li>
<li>German (GitHub #239, #245, #258)</li>
<li>Lithuanian (GitHub #233, #267)</li>
<li>Portuguese (GitHub #237)</li>
<li>Spanish (GitHub #266)</li>
</ul></li>
</ul>
<h3 id="manual-8">Manual</h3>
<ul>
<li>BugFix: Fix spelling of Git (GitHub #246)</li>
<li>Update Configuration.xml (GitHub #262)</li>
</ul>
<h3 id="internals-17">Internals</h3>
<ul>
<li>Consolidate FolderCmp (GitHub #240, #242)</li>
<li>Avoid some back and forth file path transcoding between UTF16 and UTF8 (GitHub #243)</li>
</ul>
<h2 id="winmerge-2165---2019-12-09">WinMerge 2.16.5 - 2019-12-09</h2>
<h3 id="file-compare-32">File compare</h3>
<ul>
<li>BugFix: Suspicious lack of Release() calls in lwdisp.c (GitHub #171)</li>
<li>BugFix: Performance using Unpacker (GitHub #180)</li>
<li>BugFix: WinMerge toolbar appears too small on UHD and should default to the conforming Windows 10 scaling/DPI accessibility setting (GitHub #182)</li>
<li>BugFix: &quot;Recompare as&quot; menu option doesn&#39;t follow current choice (GitHub #191)</li>
<li>BugFix: Switching between &quot;Find Next&quot; and &quot;Find Prev&quot; need to click twice (GitHub #202)</li>
<li>BugFix: Program is crashed! (GitHub #229)</li>
<li>Switch &quot;Find&quot; buttons in Find dialog (GitHub #201)</li>
<li>Improve UNICODE character support using icu.dll usable from Windows 10 Creators Update</li>
<li>Add support for color emoji (Only available in 64bit version) (To enable color emoji support, select DirectWrite* in Rendering mode combobox on Editor page in the Options dialog)<br />
Demo: <a href="https://gyazo.com/7cbbbd2c1de195fcd214d588b21b21d4">https://gyazo.com/7cbbbd2c1de195fcd214d588b21b21d4</a></li>
</ul>
<h3 id="folder-compare-28">Folder compare</h3>
<ul>
<li>BugFix: Crash when clicking Next Difference button after unchecking Show Different Items menu item</li>
<li>Changed symbols in Newer/Older column for better visual clarity (GitHub #169)</li>
<li>Add &quot;Ignore codepage differences&quot; to quick options (GitHub #197)</li>
<li>Limit the window flashing count. (GitHub #206)</li>
</ul>
<h3 id="image-compare-18">Image compare</h3>
<ul>
<li>BugFix: Image compare breaks when zoomed too much with large pictures (GitHub #188)</li>
<li>BugFix: [Image compare] Zoom-in focuses on top-left corner instead of cursor position (GitHub #211)</li>
</ul>
<h3 id="shell-extension-5">Shell extension</h3>
<ul>
<li>BugFix: &quot;Run as Administrator&quot; incompatible with &quot;Shell integration&quot; (ver 2.16.4) (sf.net #2234)</li>
</ul>
<h3 id="installer-12">Installer</h3>
<ul>
<li>BugFix: Installer russian translation (#168)</li>
<li>BugFix: Windows 10 shell integration not working (GitHub #176)</li>
<li>Installer - Proposal - Separate Inno Setup strings from WinMerge installer strings (GitHub #167)</li>
</ul>
<h3 id="translations-33">Translations</h3>
<ul>
<li>Update Italian translation (GitHub #164, #165)</li>
<li>Update Russian translation (GitHub #166)</li>
<li>Update Bulgarian translation (GitHub #170, #175, #179, #212)</li>
<li>Update Portuguese translation (GitHub #185, #199)</li>
<li>Update French translation (GitHub #187)</li>
<li>Update Lithuanian translation (GitHub #189, #208, #217, #225)</li>
<li>Update Swedish translation (GitHub #194, #196)</li>
<li>Update German translation (GitHub #204, #205, #215)</li>
<li>Update Polish translation (GitHub #218)</li>
<li>Update Simplified Chinese translation (GitHub #228)</li>
<li>Update Finnish translation (sf.net #3037)</li>
<li>Update English.pot (#216)</li>
<li>Add Japanese manual (GitHub #183)</li>
</ul>
<h3 id="manual-9">Manual</h3>
<ul>
<li>Small Manual tweaks (GitHub #190)</li>
</ul>
<h2 id="winmerge-2164---2019-07-28">WinMerge 2.16.4 - 2019-07-28</h2>
<h3 id="file-compare-33">File compare</h3>
<ul>
<li>BugFix: coretools.cpp: linelen() should not truncate lines with embedded NULs (GitHub #156)</li>
<li>BugFix: file compare : right-click doesn&#39;t select the diff under the mouse (GitHub #159)</li>
<li>BugFix: Avoid an exception in GuessCodepageEncoding() when filepath equals &quot;NUL&quot; (GitHub #162)</li>
<li>BugFix: Auto-indent did not work if the EOL-style was not CRLF</li>
</ul>
<h3 id="folder-compare-29">Folder compare</h3>
<ul>
<li>BugFix: Generating HTML Folder Compare report including File Compare report did not complete (Bitbucket #15)</li>
<li>BugFix: Compare Statistics dialog: The number of diff folders was counted in the number of diff files</li>
</ul>
<h3 id="plugins-22">Plugins</h3>
<ul>
<li>BugFix: PrediffLineFilter.sct: Wrong encoding for settings dialog (Bitbucket #16)</li>
</ul>
<h3 id="translations-34">Translations</h3>
<ul>
<li>Update Russian translation (Bitbucket PR #51)</li>
<li>Update Italian translation (Bitbucket PR #52)</li>
</ul>
<h3 id="internals-18">Internals</h3>
<ul>
<li>Favor PathContext::GetSize() over PathContext::size() (GitHub #157)</li>
<li>Consolidate FolderCmp (GitHub #158, #160, #161)</li>
<li>Avoid some InnoSetup compiler warnings (Bitbucket PR #53)</li>
</ul>
<h2 id="winmerge-2163---2019-06-29">WinMerge 2.16.3 - 2019-06-29</h2>
<h3 id="general-26">General</h3>
<ul>
<li>BugFix: Slow startup with documents folder redirected to high-latency network drive (Bitbucket #155)</li>
<li>Add VisualElementsManifest for Windows 10 start menu (Bitbucket PR #47)</li>
<li>Reduce the size of the executable file</li>
</ul>
<h3 id="file-compare-34">File compare</h3>
<ul>
<li>BugFix: Location and Diff pane visibility broken in 2.16.1 (GitHub #138, Bitbucket #163, sf.net #2228)</li>
<li>BugFix: Temporary files could not be created (GitHub #143, sf.net #2220)</li>
<li>BugFix: Trivial bugfix (GitHub #155)</li>
<li>BugFix: Regression: Incorrectly shows &#39;no changes&#39; for large portions of certain binary files! (Bitbucket #162)</li>
<li>Small tweaks for HTML file reports (Bitbucket PR #44, #45)</li>
<li>Made diff algorithm selectable by using libXDiff algorithms: default(Myers), minimal, patience, histogram</li>
<li>triple click to select whole line (Bitbucket #144)</li>
<li>Add support for merging word level diffs in selection<br />
Demo: <a href="https://i.gyazo.com/af18960bd1f121213a2cd9287cae9cf4.gif">https://i.gyazo.com/af18960bd1f121213a2cd9287cae9cf4.gif</a></li>
</ul>
<h3 id="folder-compare-30">Folder compare</h3>
<ul>
<li>BugFix: WinMerge 3-Way Compare Bugs: Always Shows Unique Items. (GitHub #154)</li>
<li>BugFix: WinMerge could not compare files that are opened in other applications</li>
<li>Show progress bar while generating Folder Compare Report</li>
<li>Use own colors settings for folder compare (Bitbucket PR #49)</li>
</ul>
<h3 id="project-file-5">Project file</h3>
<ul>
<li>BugFix: Possible Bugs in 2.16.2 (sf.net #2221)</li>
<li>Supporting relative path in project file (Bitbucket #158)</li>
<li>Allow multiple &lt;paths&gt; in project file</li>
</ul>
<h3 id="image-compare-19">Image compare</h3>
<ul>
<li>BugFix: no message was displayed when file saving failed</li>
<li>Implement Insertion/Deletion Detection<br />
Demo: <a href="https://gyazo.com/17d8773354d23b5ae51262f28b0f1f80">https://gyazo.com/17d8773354d23b5ae51262f28b0f1f80</a></li>
</ul>
<h3 id="options-dialog-18">Options dialog</h3>
<ul>
<li>Tweak size of combobox &quot;codepage&quot; in options (GitHub #144)</li>
<li>Sort combobox codepage and add manual codepage (GitHub #145)</li>
</ul>
<h3 id="installer-13">Installer</h3>
<ul>
<li>Allow per-user installation (only x64 installer)</li>
<li>Don&#39;t install Files.txt and don&#39;t add &quot;Uninstall WinMerge&quot; to the start menu (Bitbuket #38)</li>
</ul>
<h3 id="translations-35">Translations</h3>
<ul>
<li>Update French translation (GitHub #149, #150)</li>
<li>Update Lithuanian translation (Bitbucket PR #36, #40, #43, #46, #48, #50)</li>
<li>Update Russian translation (Bitbucket PR #41, #42)</li>
</ul>
<h2 id="winmerge-2162---2019-04-04">WinMerge 2.16.2 - 2019-04-04</h2>
<h3 id="file-compare-35">File compare</h3>
<ul>
<li>BugFix: Edit &gt; Replace &gt; All, Undo: should undo all (sf.net #2113)</li>
<li>BugFix: Save As: default directory should be file&#39;s original directory (sf.net #2163)</li>
<li>BugFix: Strings with embedded 0 bytes are handled incorrectly (sf.net #2187)</li>
<li>BugFix: Removed word level merge support due to unstable behavior</li>
<li>BugFix: Menu item &quot;File-&gt;File Encoding&quot; moves main window (Bitbucket #150)</li>
<li>BugFix: Crash when maximizing MDI child window</li>
<li>BugFix: Crash when failed to load file</li>
<li>Add syntax highlight for Lua (Bitbucket #114)</li>
</ul>
<h3 id="folder-compare-31">Folder compare</h3>
<ul>
<li>BugFix: Compare results refresh incorrectly after deletions (sf.net #2217)</li>
</ul>
<h3 id="archive-support-10">Archive Support</h3>
<ul>
<li>Upgraded to 7-zip 19.00</li>
</ul>
<h3 id="tab-bar">Tab bar</h3>
<ul>
<li>Improve icon display at 120DPI</li>
</ul>
<h3 id="image-compare-20">Image compare</h3>
<ul>
<li>BugFix: Zoom In/Out shortcuts don&#39;t update zoom level in location pane (Bitbucket #149)</li>
<li>Zoom In/Out menu items for image compare (Bitbucket #148)</li>
</ul>
<h3 id="open-dialog-3">Open dialog</h3>
<ul>
<li>Allow quoted filenames in &quot;Select Files or Folders&quot; (sf.net #1240, GitHub #137)</li>
</ul>
<h3 id="manual-10">Manual</h3>
<ul>
<li>Some tweaks for the manual (Bitbucket PR #35)</li>
</ul>
<h3 id="installer-14">Installer</h3>
<ul>
<li>BugFix: Gibberish language during installation (Bitbucket #147)</li>
</ul>
<h3 id="translations-36">Translations</h3>
<ul>
<li>Update Catalan translation (Bitbucket PR #29)</li>
<li>Update Italian translation (Bitbucket PR #32)</li>
<li>Update Lithuanian translation (Bitbucket PR #33)</li>
<li>Update Simplified Chinese translation (Bitbucket PR #34)</li>
</ul>
<h2 id="winmerge-2161---2019-02-24">WinMerge 2.16.1 - 2019-02-24</h2>
<h3 id="general-27">General</h3>
<ul>
<li>Updated the copyright year to 2019, actually (GitHub #110)</li>
<li>Prevent splitter view from claiming input focus (GitHub #127)</li>
<li>Contributors.txt: Changed EOL from Unix (LF) to Windows (CR LF), because not shows correctly in Windows Notepad. (Bitbucket PR #17)</li>
</ul>
<h3 id="open-dialog-4">Open dialog</h3>
<ul>
<li>Alternatively to Swap buttons, allow drag &amp; drop between filetype icons (GitHub #118)</li>
<li>Expose cleaner moving/resizing behavior esp. in non-maximized state (GitHub #119)</li>
<li>Avoid retaining bogus filetype icons from previous selections (GitHub #122)</li>
</ul>
<h3 id="file-compare-36">File compare</h3>
<ul>
<li>BugFix: Extra blank displayed after left/right single/double quotes (Bitbucket #134)</li>
<li>BugFix: Click doesn&#39;t trigger document to scroll in Location pane under circumstance (Bitbucket #140)</li>
<li>BugFix: Frustrating text selection bug (GitHub #109)</li>
<li>BugFix: Left and Right files swap after losing focus v2.16.0.0 (sf.net #2213)</li>
<li>BugFix: WinMerge 2.16.0.0 Unicoe X64 version displaying Microsoft Visual C++ Runtime Library Asset in DiffWraper.CPP line 984 (sf.net #2214)</li>
<li>BugFix: Cancelation of selection range with reverse indent (sf.net #2215)</li>
<li>BugFix: upward and downward search not work correctly</li>
<li>Bugifx: Fix menu accelerator conflict</li>
<li>Add File/Recompare As/Image menu item</li>
<li>Add support for merging word level diffs in selection<br />
Demo: <a href="https://i.gyazo.com/af18960bd1f121213a2cd9287cae9cf4.gif">https://i.gyazo.com/af18960bd1f121213a2cd9287cae9cf4.gif</a></li>
</ul>
<h3 id="folder-compare-32">Folder compare</h3>
<ul>
<li>BugFix: Unique files are not shown (Bitbucket #138)</li>
<li>BugFix: Improve Hard-IO error handling, other bug fixes, cleanup, tweaks (GitHub #120)</li>
</ul>
<h3 id="archive-support-11">Archive Support</h3>
<ul>
<li>Upgraded to 7-zip 18.06 (Bitbucket #119)</li>
</ul>
<h3 id="options-dialog-19">Options dialog</h3>
<ul>
<li>BugFix: Widen the width of labels in Options dialog (GitHub#108)</li>
<li>BugFix: view settings make winmerge crash (Bitbucket #109)</li>
<li>BugFix: 64bit version can&#39;t browse between option pages (Bitbucket #128)</li>
<li>BugFix: Shell Integration &quot;Enable Advanced Menu&quot; disabled on new install (Bitbucket #137)</li>
<li>BugFix: Binary File options don&#39;t change from default (Bitbucket #135)</li>
</ul>
<h3 id="shell-extension-6">Shell extension</h3>
<ul>
<li>BugFix: CreateTranslatedRcFiles needs an update (GitHub #113)</li>
</ul>
<h3 id="translations-37">Translations</h3>
<ul>
<li>Update Brazilian Portuguese translation (GitHub #108)</li>
<li>Update Swedish translation (sf.net #3035, GitHub #112, #114)</li>
<li>Update Lithuanian translation (Bitbucket PR #7, #8, #27)</li>
<li>Update Russian translation (Bitbucket PR #9-#26, #139)</li>
<li>Update Bulgarian translation (Bitbucket PR #28)</li>
<li>Update Turkish translation (sf.net #3036)</li>
<li>Update Japanese translation</li>
<li>Remove now unneeded MergeLang.dll</li>
</ul>
<h3 id="manual-11">Manual</h3>
<ul>
<li>BugFix: Some fixes in the manual (GitHub #116)</li>
<li>Bugifx: end of line documentation (sf.net #2211)</li>
<li>Clarification as per <a href="https://github.com/WinMerge/winmerge-v2/issues/41">https://github.com/WinMerge/winmerge-v2/issues/41</a> (GitHub #126)</li>
</ul>
<h3 id="internals-19">Internals</h3>
<ul>
<li>Remove <code>nFinalNullLines</code> checking, disabled by an earlier commit (GitHub #111)</li>
<li>Various minor updates, plus preparation for VS2019 (GitHub #115)</li>
<li>DiffTextBuffer: Create Unicode temp files (makes EscapeControlChars() obsolete) (GitHub #123)</li>
<li>Don&#39;t pass <code>DIFFITEM *</code> by casting to <code>uintptr_t</code> (GitHub #124, #125)</li>
</ul>
<h2 id="winmerge-2160---2018-11-23">WinMerge 2.16.0 - 2018-11-23</h2>
<h3 id="general-28">General</h3>
<ul>
<li>BugFix: Disabled icon on toolbar not gray out when running on XP</li>
</ul>
<h3 id="file-compare-37">File compare</h3>
<ul>
<li>BugFix: Copy to X and Advance&quot; not work in 3way-compare (Bitbucket #123)</li>
<li>BugFix: File compare always showing different ending (GitHub #106)</li>
<li>BugFix: Scrolling issue (GitHub #105)</li>
<li>BugFix: A crash (GitHub #104)</li>
<li>Add separators to &quot;Zoom&quot; and &quot;Diff Context&quot; menus (Bitbucket PR #5)</li>
</ul>
<h3 id="open-dialog-5">Open dialog</h3>
<ul>
<li>BugFix: Drag&amp;Drop in Open Dialog won&#39;t work correct (Bitbucket #125)</li>
</ul>
<h3 id="installer-15">Installer</h3>
<ul>
<li>Drop ClearCase integration (Bitbucket PR #6)</li>
</ul>
<h3 id="translations-38">Translations</h3>
<ul>
<li>Add Lithuanian translation (Bitbucket #124)</li>
<li>Update Portuguese translation (GitHub #102,#103)</li>
</ul>
<h2 id="winmerge-2155---2018-10-28">WinMerge 2.15.5 - 2018-10-28</h2>
<h3 id="general-29">General</h3>
<ul>
<li>Add support for very long path and file names (GitHub #87,#95,#99)</li>
<li>Enable Ctrl+C shortcut key on the file path bar</li>
<li>Remove support for old version control system (GitHub #96)</li>
<li>Improve Options &gt; Color &gt; Differences dialog layout (GitHub #89)</li>
<li>Improve information and formatting in Help &gt; Configuration reporting (GitHub #92)</li>
<li>Improve visual layout of Open dialog (GitHub #97)</li>
<li>Add icon for &quot;New (3 panes)&quot; menu item (GitHub #97)</li>
</ul>
<h3 id="file-compare-38">File compare</h3>
<ul>
<li>Add Window/Split menu item</li>
<li>Improve handling of last lines in files (GitHub #89,#90,#93)</li>
<li>Improve &quot;Visual C++ Loose&quot; filter (GitHub #95)</li>
<li>BugFix: Crash when saving a file if the active pane is diff detail pane</li>
<li>BugFix: Crash when pressing Alt+Down key while loading large files</li>
<li>BugFix: Crash when &quot;mousing over&quot; Plugin Setting context menu if Plugins are disabled</li>
<li>BugFix: Various problems reading of files &gt; 2GB (GitHub #81)</li>
<li>BugFix: Selecting files &quot;From MRU list&quot; again works correctly</li>
</ul>
<h3 id="translations-39">Translations</h3>
<ul>
<li>Update Italian translation (GitHub #86)</li>
<li>Update Japanese translation (Bitbucket #3)</li>
<li>Update Dutch translation</li>
<li>Update German translation</li>
<li>BugFix: Spanish translation error (Bitbucket Issue #108)</li>
</ul>
<h3 id="internals-20">Internals</h3>
<ul>
<li>Various diffengine and compilation/build fixes (GitHub #89,#90,#91,#92,#94)</li>
</ul>
<h2 id="winmerge-2154---2018-04-28">WinMerge 2.15.4 - 2018-04-28</h2>
<h3 id="appearance">Appearance</h3>
<ul>
<li>BugFix: Dot icon in a drop-down menu is not drawn correctly</li>
</ul>
<h3 id="file-compare-39">File compare</h3>
<ul>
<li>BugFix: Crash when reading a file in Google Drive File Stream (sf.net#2206)</li>
<li>BugFix: Crash when removing ghost lines (GitHub #80)</li>
<li>BugFix: Lines without EOL appears even though it is not the last line</li>
<li>Add Ctrl++/Ctrl+-(not numpad key) shortcut key to zoom in/out (sf.net#1727)</li>
<li>Remove 2GB file size limit for 64-bit build (GitHub #81, #82)</li>
</ul>
<h3 id="folder-compare-33">Folder compare</h3>
<ul>
<li>Add Pause button to Folder Compare Progress dialog (sf.net#828,#1222,#1237)</li>
<li>Re-detect the file encoding when opening files (sf.net#2131)</li>
</ul>
<h3 id="open-dialog-6">Open dialog</h3>
<ul>
<li>Add Options button to Open dialog</li>
<li>Add Save Project button to Open dialog</li>
</ul>
<h3 id="patch-generator-dialog-5">Patch generator dialog</h3>
<ul>
<li>BugFix: File1/File2 fields don&#39;t remember manual input (sf.net#1949)</li>
</ul>
<h3 id="reports-2">Reports</h3>
<ul>
<li>BugFix: Garbled text was copied to the clipboard when generating a report on the clipboard (sf.net#2200)</li>
</ul>
<h3 id="plugins-23">Plugins</h3>
<ul>
<li>Add PrediffLineFilter.sct plugin</li>
<li>Make ignored lines by Prediffer plugin colored</li>
</ul>
<h3 id="translations-40">Translations</h3>
<ul>
<li>Update Slovak translation (sf.net#2902)</li>
<li>Update Portuguese translation (GitHub #84,#85)</li>
</ul>
<h3 id="internals-21">Internals</h3>
<ul>
<li>Adapt to VS2017 version 15.6.1 (GitHub #79)</li>
</ul>
<h2 id="winmerge-2153---2018-03-04">WinMerge 2.15.3 - 2018-03-04</h2>
<h3 id="file-compare-40">File compare</h3>
<ul>
<li>Fix assertion error when started on Windows XP</li>
<li>Fix a problem that moved blocks are sometimes shown as normal diff blocks</li>
<li>Fix a problem that EOL is removed unexpectedly when removing lines included ghost lines</li>
<li>Fix reverse search to search last line correctly</li>
<li>Fix infinite loop when replacing ^ (sf.net#2094)</li>
<li>Fix to show text that contains &amp; on message box, correctly (sf.net#2067)</li>
</ul>
<h3 id="folder-compare-34">Folder compare</h3>
<ul>
<li>Fix a problem that unique files are invisible when comparing files using &#39;Date&#39; compare method</li>
<li>Remove trailing garbage from a dragged text (Github#75)</li>
</ul>
<h3 id="patch-generator-dialog-6">Patch generator dialog</h3>
<ul>
<li>Fix a problem that typed-in Context in Generate Patch is not applied (sf.net#2179)</li>
</ul>
<h3 id="reports-3">Reports</h3>
<ul>
<li>Entitify file paths in HTML &amp; XML directory comparison reports (sf.net#2199)</li>
</ul>
<h3 id="location-pane">Location pane</h3>
<ul>
<li>Add support for HiDPI screens (Github #54)</li>
</ul>
<h3 id="plugins-24">Plugins</h3>
<ul>
<li>CompareMSExcelFiles.sct: Add &quot;Compare headers and footers&quot; checkbox to settings dialog (sf.net#2102)</li>
</ul>
<h3 id="installer-16">Installer</h3>
<ul>
<li>Remove extra space in installer: &quot; Launch WinMerge&quot; (sf.net#2144)</li>
<li>Add &quot;How to Apply These Terms to Your New Programs&quot; section into GPL.rtf (sf.net#2098)</li>
</ul>
<h3 id="translations-41">Translations</h3>
<ul>
<li>Add Finnish translation (sf.net#3031)</li>
<li>Add Sinhala translation (sf.net#3032)</li>
<li>Update Korean translation (Github #61)</li>
<li>Update Portuguese translation (GitHub #56-78)</li>
<li>Update Polish translation (sf.net#2177)</li>
<li>Update Turkish translation (Bitbucket#85, #86)</li>
<li>Change the file encodings of all .po files of WinMerge GUI to UTF-8</li>
</ul>
<h2 id="winmerge-2152---2018-01-28">WinMerge 2.15.2 - 2018-01-28</h2>
<h3 id="appearance-1">Appearance</h3>
<ul>
<li>Flatten GUI pane edges</li>
<li>Partial support for HiDPI (not Per-Monitor DPI Aware)</li>
<li>Remove splash screen. The splash screen image is now displayed in About dialog</li>
</ul>
<h3 id="tool-bar">Tool bar</h3>
<ul>
<li>Add &quot;View-&gt;Toolbar-&gt;Huge&quot; menu item</li>
<li>Add drop down menu to Options icon</li>
<li>Add icons for 3-way merge functions</li>
</ul>
<h3 id="open-dialog-7">Open dialog</h3>
<ul>
<li>Change window type of Open dialog from modal window to MDI child window</li>
<li>Add Read-only checkbox to Open dialog</li>
<li>Add buttons to swap items</li>
<li>Add the third path combobox for 3-way comparison</li>
<li>Allow file name patterns like not only <em>.ext but file</em>.ext</li>
</ul>
<h3 id="tab-bar-1">Tab bar</h3>
<ul>
<li>Make Tabbar reorderable by using drag-and-drop</li>
<li>Add &quot;Close Other Tabs&quot;, &quot;Close Right Tabs&quot; and &quot;Close Left Tabs&quot; menu items to system menu</li>
<li>Add &quot;Enable Auto Max Width&quot; menu item to Tab Bar context menu</li>
<li>Show close button when hovering over tabs</li>
<li>Add icons to each tab</li>
</ul>
<h3 id="options-dialog-20">Options dialog</h3>
<ul>
<li>Add &quot;Close Select Files or Folders Dialog on clicking OK button&quot; checkbox to &quot;General&quot; page</li>
<li>Add &quot;Language&quot; combobox to &quot;General&quot; page and remove Language dialog</li>
<li>Add &quot;Ignore codepage differences&quot; checkbox to &quot;Compare/General&quot; page</li>
<li>Remove checkboxes in &quot;Enable multiple compare windows for&quot; group from &quot;General&quot; page (Now always enabled multi compare windows)</li>
<li>Add &quot;Include Subfolders&quot; checkbox to &quot;Compare/Folder&quot; page</li>
<li>Add &quot;Automatically expand all subfolders&quot; checkbox to &quot;Compare/Folder&quot; page</li>
<li>Add &quot;Ignore Reparse Points&quot; checkbox to &quot;Compare/Folder&quot; page</li>
<li>Add &quot;Number of compare threads&quot; textbox to &quot;Compare/Folder&quot; page</li>
<li>Add &quot;Compare/Binary&quot; page</li>
<li>Add &quot;Compare/Image&quot; page</li>
<li>Add checkboxes whether to use custom colors for text to &quot;Colors/Differences&quot; page</li>
<li>Add &quot;GitHub/Bitbucket&quot; button to &quot;Colors/Differences&quot; page</li>
<li>Add &quot;Margin&quot; color button to &quot;Colors/Text&quot; page</li>
<li>Add &quot;Colors/Markers&quot; page</li>
<li>Remove &quot;Use stand-alone 7-Zip if available&quot; and &quot;Use local 7-Zip from WinMerge folder&quot; radio button from &quot;Archive Support&quot; page (Now always use local 7-Zip from WinMerge folder)</li>
<li>Make $file and $linenum parameters available in External editor textbox on &quot;System&quot; page</li>
<li>Make Custom codepage on &quot;Codepage&quot; page selectable from dropdown list</li>
<li>Add &quot;Detect codepage for text files using mlang.dll&quot; checkbox to &quot;Codepage&quot; page</li>
<li>Remove &quot;Include subfolders by default&quot; checkbox from &quot;Shell Integration&quot; page (ShellExtension now refers to &quot;Include subfolders&quot; checkbox on &quot;Compare/Folder&quot; page or on &quot;Select Files or Folders&quot; dialog)</li>
<li>Remove &quot;Add Shell menu to context menu&quot; from &quot;Shell Integration&quot; page (WinMerge now always adds shell menu to context menu in Folder window)</li>
<li>Add &quot;Register shell extension&quot; button to &quot;Shell Integration&quot; page</li>
</ul>
<h3 id="folder-compare-35">Folder Compare</h3>
<ul>
<li>Add Multi-thread compare support for Full Contents or Quick Contents method</li>
<li>Color the background of items in list view</li>
<li>Restore collapse/expand state of folder tree after re-comparison</li>
<li>Make comparing window closeable</li>
<li>Make sub-folder re-comparable</li>
<li>Display folder items in list view during comparison</li>
<li>Display the file name currently being compared in progress window</li>
<li>Add &quot;Parent Folder&quot; menu item in &#39;Open Left/Middle/Right&#39; menu</li>
<li>Add &quot;Compare Non-horizontally&quot; menu item to context menu</li>
<li>Add &quot;Copy items To Clipboard&quot; menu item to context menu</li>
<li>Add &quot;Swap panes&quot; menu item to View menu</li>
<li>Add &quot;Auto/Manual Prediffer&quot; menu item to Plugin menu</li>
<li>Open selected folders in new tab when clicking &quot;Compare&quot; menu item with pressing Ctrl key</li>
<li>Cleanup Date column alignment (GitHub #25)</li>
<li>BugFix: Version column not sorted correctly</li>
<li>BugFix: A unique item that is a binary file was not detected as a binary file</li>
<li>BugFix: Properly handle 3-way comparison of Binary files (Github #23)</li>
<li>BugFix: Use proper font for View&gt;Use Default Font menu item (GitHub #24)</li>
</ul>
<h3 id="file-compare-41">File Compare</h3>
<ul>
<li>Add support for 3-way compare/merge</li>
<li>Add &quot;Diff Context&quot; menu to View menu to adjust the number of displayed diff context lines</li>
<li>Add &quot;Split Vertically&quot; menu item to View menu</li>
<li>Add &quot;Add Synchronization Point&quot; to Merge menu</li>
<li>Add &quot;Auto/Manual Prediffer&quot; menu item to Plugin menu</li>
<li>Show the name of the applied plugin in the status bar</li>
<li>Add shortcut(Ctrl+Alt+E) to open with external editor</li>
<li>Add Go and Rust highlighter</li>
<li>Add VHDL syntax highlighter (Bitbucket #2)</li>
<li>Add C++11 keywords</li>
<li>Make merge editor window D&amp;Dable</li>
<li>Highlight the search text and implement Marker</li>
<li>Highlight the EOL pane in status bar if there is a difference in the EOL of files</li>
<li>Reload files when another application changes files opened by WinMerge</li>
<li>Improve in-line diff algorithm</li>
<li>BugFix: More on Issue #73 Fixed the -dl -dm and -dr description parameters for conflict files (for both 2 way and 3 way conflict files).(Bitbucket #1)</li>
<li>BugFix: Fix Memory Leak with Drop Targets (GitHub #26)</li>
</ul>
<h3 id="binary-compare-11">Binary Compare</h3>
<ul>
<li>Install binary file editor (frhed) component again by default</li>
<li>Make binary file editor undoable</li>
</ul>
<h3 id="image-compare-21">Image Compare</h3>
<ul>
<li>Add image file compare/merge support</li>
</ul>
<h3 id="reports-4">Reports</h3>
<ul>
<li>Add &quot;Include File Compare Report&quot; checkbox to &quot;Folder Compare Report&quot; dialog</li>
<li>Change the encoding of HTML folder compare report from ANSI to UTF-8</li>
<li>Add icons to the HTML folder compare report</li>
<li>Change visual style of HTML report</li>
</ul>
<h3 id="archive-support-12">Archive Support</h3>
<ul>
<li>Install 7-zip plugin by default</li>
</ul>
<h3 id="drag-and-drop">Drag and Drop</h3>
<ul>
<li>Accept drag&amp;droped files or folders from various places like: Zip folder, FTP folder, Recycle bin, images in Web browser</li>
</ul>
<h3 id="plugins-25">Plugins</h3>
<ul>
<li>Introduce new plugin type FILE_FOLDER_PACK_UNPACKER which allows unpacking data in a file to multiple files in a folder</li>
<li>Use FILE_FOLDER_PACK_UNPACKER plugin for decompressing archives</li>
<li>Add support for unpacker plugin written in VBScript/Javascript.</li>
<li>Add WinMerge32BitPluginProxy.exe to make 32-bit plugins usable in 64-bit WinMerge</li>
<li>Rewrite some plugins in VBScript</li>
<li>Add CompareMSPowerPointFiles.sct plugin</li>
<li>Add ApplyPatch.sct plugin</li>
<li>Add SortAscending, SortDescending and ExecFilterCommand menu item to Plugin-&gt;Script menu</li>
<li>Make plugins configurable with settings dialog of each plugin</li>
</ul>
<h3 id="filters-2">Filters</h3>
<ul>
<li>Update *.flt files for Visual Studio 2015 and 2017</li>
</ul>
<h3 id="shell-extension-7">Shell extension</h3>
<ul>
<li>Display another WinMerge icon in Explorer&#39;s context menu when a folder is selected</li>
</ul>
<h3 id="installer-17">Installer</h3>
<ul>
<li>64-bit version of WinMerge is available</li>
<li>Install plugins by default</li>
</ul>
<h3 id="translations-42">Translations</h3>
<ul>
<li>Update Portuguese translation (GitHub #2-17)</li>
<li>Update Korean translation (GitHub #45)</li>
<li>Update Traditional Chinese translation (GitHub #53)</li>
<li>Remove &quot;Language&quot; menu item from View menu (You can specify UI language at Options dialog)</li>
</ul>
<h3 id="command-line-9">Command line</h3>
<ul>
<li>Incompatible change: The third file path is now treated as a right path for 3-way comparison, not output path Use /o option for ouput path instead</li>
<li>Add /wm, /dm, /fl, /fm, /fr, /al, /am and /ar option for 3-way comparison</li>
</ul>
<h3 id="other-changes">Other changes</h3>
<ul>
<li>Add support for jump list introduced in Windows 7</li>
</ul>
<h3 id="internals-22">Internals</h3>
<ul>
<li>Use POCO C++ libraries instead of pcre, expat and scew. They use pcre, expat internally</li>
<li>Link statically with MFC and VC runtime libraries</li>
<li>Various compilation/build fixes (GitHub #1,#19,#21,#29,#31-33,#36-38,#42-44,#49-52)</li>
</ul>
<h2 id="winmerge-2140---2013-02-02-r7596">WinMerge 2.14.0 - 2013-02-02 (r7596)</h2>
<ul>
<li>BugFix: Shell extension uses unquoted program path (patches#3023)</li>
<li>Translation updates
<ul>
<li>Dutch (patches:#3020)</li>
<li>Slovenian (patches:#3021)</li>
</ul></li>
</ul>
<h2 id="winmerge-21322---2013-01-13-r7585">WinMerge 2.13.22 - 2013-01-13 (r7585)</h2>
<ul>
<li>Translation updates
<ul>
<li>Turkish (patches:#2967)</li>
<li>Russian (patches:#3017)</li>
<li>Norwegian (patches:#3018)</li>
<li>Danish (patches:#3019)</li>
</ul></li>
</ul>
<h2 id="winmerge-21321---2012-12-30-r7575">WinMerge 2.13.21 - 2012-12-30 (r7575)</h2>
<ul>
<li>Update PCRE to version 8.10</li>
<li>Update SCEW to version 1.1.2</li>
<li>Improve startup time (#2788142)</li>
<li>Add menuitems for selecting automatic or manual prediffing (#2638608)</li>
<li>Add accelerator keys for Shell context menu (#2823536)</li>
<li>Improve editing of linefilter regular expressions (#3015416)</li>
<li>Allow editing context line count in patch creator (#2092180)</li>
<li>Improve color options organization (#2818451)</li>
<li>Add /xq command line switch for closing WinMerge after identical files and not showing message (#2827836)</li>
<li>Allow setting codepage from command line (#2725549)</li>
<li>Allow giving encoding name as custom codepage (#2813825, #3010934)</li>
<li>Add new options dialog panel for folder compare options (#2819626)</li>
<li>Add options GUI for quick compare limit (#2825628)</li>
<li>Write config log as UTF-8 file (r7057)</li>
<li>BugFix: Untranslated string (&quot;Merge.rc:nnnn&quot;) was displayed in status bar (#3025855)</li>
<li>BugFix: Pane headers not updated after language change (#2923684)</li>
<li>BugFix: Quick contents compare didn&#39;t ignore EOL byte differences (#2929005)</li>
<li>BugFix: Compare by size always checked file times too (#2919510)</li>
<li>BugFix: Crash when pasting from clipboard (#3109525)</li>
<li>BugFix: Keeps verifing path even turned off in options (#3111581)</li>
<li>BugFix: Crash after deleting text (#3109521)</li>
<li>BugFix: Added EOL chars between copied file/path names (#2817546)</li>
<li>BugFix: Created new matching folder to wrong folder (#2890961)</li>
<li>BugFix: Strange scrolling effect in location pane (#2942869)</li>
<li>BugFix: Plugin error after interrupting folder compare (#2919475)</li>
<li>BugFix: &quot;+&quot; and &quot;-&quot; from the number block don&#39;t work in the editor (#3306182)</li>
<li>BugFix: Date format did not respect Regional Settings (#3175189)</li>
<li>BugFix: When selecting multiple files in Patch Generator dialog, &quot;Swap&quot; button led to an error. (#3043635, #3066200)</li>
<li>BugFix: WinMerge contained a vulnerability in handling project files (#3185386) (<a href="http://www.zeroscience.mk/mk/vulnerabilities/ZSL-2011-4997.php">http://www.zeroscience.mk/mk/vulnerabilities/ZSL-2011-4997.php</a>)</li>
<li>Installer: Remove OpenCandy from the InnoSetup installer (r7572, r7539)</li>
<li>New translation: Basque (#3387142)</li>
<li>Translation updates
<ul>
<li>French (#3412030)</li>
<li>Hungarian (#3164982)</li>
<li>Spanish (#3412937)</li>
</ul></li>
</ul>
<h2 id="winmerge-21320---2010-10-20-r7319">WinMerge 2.13.20 - 2010-10-20 (r7319)</h2>
<ul>
<li>Add missing keywords to Pascal highlighter (#2834192)</li>
<li>Recognize .ascx files as ASP files (#3042393)</li>
<li>Fix help locations (#2988974)</li>
<li>Show only &quot;Copy to other side&quot; item in file compare context menu (#2600787)</li>
<li>Expand/collapse folders from keyboard (#2203904)</li>
<li>Improve detecting XML files in file compare (#2726531)</li>
<li>Initialize folder selection dialog to currently selected folder in options dialog (r6570)</li>
<li>New translation: Persian (#2877121, #3065119)</li>
<li>New translation: Serbian (#3017674, #3065119)</li>
<li>Installer: Drop Windows 9x/ME/NT4 support and use Microsoft runtime libraries installer (#3070254)</li>
<li>Installer: Remove Uninstall shortcut from start menu folder (#3076909)</li>
<li>Installer: Don&#39;t install quick launch icon for Windows 7 (#3079966)</li>
<li>Installer: Add OpenCandy to the InnoSetup installer (#3088720)</li>
<li>BugFix: WinMerge was vulnerable to DLL hijacking as described in Microsoft Security Advisory (2269637) (#33056008)</li>
<li>BugFix: Location pane focus enabled &quot;Save&quot; (#3022292)</li>
<li>BugFix: &quot;Copy and advance&quot; toolbar icons not automatically enabled (#3033325)</li>
<li>Translation updates
<ul>
<li>Bulgarian (#3082392)</li>
<li>Chinese (#3033324)</li>
<li>Dutch (#2804979)</li>
<li>French (#2850842, #2968200)</li>
<li>Slovenian (#2917796, #2932094, #2934354, #3070136)</li>
<li>Spanish (#2930734)</li>
<li>Turkish (#2825132, #2827817)</li>
<li>Ukrainian (#2817835)</li>
</ul></li>
</ul>
<h2 id="winmerge-2124---2009-06-09-r6833">WinMerge 2.12.4 - 2009-06-09 (r6833)</h2>
<ul>
<li>Add more C/C++ types to syntax highlight (#2776705)</li>
<li>Create config log file to My Documents-folder (#2789839)</li>
<li>BugFix: Reports had same left/right descriptions (#2772646)</li>
<li>BugFix: When conflict file saved trailing line-ending was lost (#2550412)</li>
<li>BugFix: File compare HTML reports use invald DOCTYPE (#2783477)</li>
<li>BugFix: &quot;##&quot; in file filter pattern caused an infinite loop (#2789345)</li>
<li>BugFix: Could select prediffer when plugins were disabled (#2787131)</li>
<li>BugFix: Opening mixed-EOL file one side forced both files to be handled as mixed-eol files (#2022312)</li>
<li>BugFix: Didn&#39;t show correct EOL chars if whitespace chars were turned off and again on (#2791402)</li>
<li>BugFix: Generated patch had wrong file time for UTF-16 files (#2791506)</li>
<li>BugFix: Did not export changed options values (#2799149)</li>
<li>BugFix: Reset the compare method to original if it was changed (#2802427)</li>
</ul>
<h2 id="winmerge-2122---2009-04-01-r6625">WinMerge 2.12.2 - 2009-04-01 (r6625)</h2>
<ul>
<li>Disable folder compare tree-view by default (#2714968)</li>
<li>BugFix: Filename extension column was empty for files (#2670046)</li>
<li>BugFix: Crash when selecting backup folder (#2686382)</li>
<li>BugFix: Swapping panes did not update statusbar (#2661838)</li>
<li>BugFix: Says files are identical after making files different in another application and re-loading them (#2672737)</li>
<li>BugFix: Files with missing last EOL weren&#39;t merged correctly (#2712120)</li>
<li>Translation updates
<ul>
<li>Polish (#2717382, #2720875)</li>
</ul></li>
</ul>
<h2 id="winmerge-2120---2009-03-05-r6542">WinMerge 2.12.0 - 2009-03-05 (r6542)</h2>
<ul>
<li>Remember last target folder for Copy/Move to in folder compare (#2659228)</li>
<li>Detect Windows 7 and Windows 2008 in config log (#2599256)</li>
<li>BugFix: Help window opened from WinMerge was modal (#2590079)</li>
<li>BugFix: Crash in editor when undoing (#2590212)</li>
<li>BugFix: Browse button in Option dialog reverted to previous settings (#2606153)</li>
<li>BugFix: Files without extension get backed up to name..bak (#2627752)</li>
<li>BugFix: Must Show Different to Show L/R Unique (#2129561)</li>
<li>Translation updates
<ul>
<li>Chinese Traditional (#2608190)</li>
<li>Croatian (#2596949)</li>
<li>Czech (r6538)</li>
<li>Danish (#2648183)</li>
<li>Dutch (#2586422, #2590430)</li>
<li>French (#2603591)</li>
<li>Galician (#2632173)</li>
<li>German (#2586497)</li>
<li>Greek (#2615285)</li>
<li>Japanese (r6456)</li>
<li>Swedish (#2586274)</li>
<li>Ukrainian (#2609242)</li>
</ul></li>
</ul>
<h2 id="winmerge-2112---2009-02-05-r6428">WinMerge 2.11.2 - 2009-02-05 (r6428)</h2>
<ul>
<li>Update Python syntax highlight keywords for Python 2.6 (#2473375)</li>
<li>More accurate visible area rect in location pane (#2479804)</li>
<li>New options page for Shell Integration options (#2433648)</li>
<li>Remove Frhed hex editor from GUI (context menu) (r6376)</li>
<li>Register.bat should use Find with full path (#2536582)</li>
<li>Better Unicode file detection for full contents compare (2477657)</li>
<li>BugFix: Could not open project file that contains an ampersand character in folder names (#2372000)</li>
<li>BugFix: File compare method &quot;Modified Date and Size&quot; ignored file size (#2455595)</li>
<li>BugFix: Quick compare didn&#39;t ignore EOL differences (#2433677)</li>
<li>BugFix: Remove EOL characters from command line (#2458728)</li>
<li>BugFix: Merging one line diff to empty other side didn&#39;t work (#2456399)</li>
<li>BugFix: Location pane was empty for some files with diffs (#2459234)</li>
<li>BugFix: Line filter editing didn&#39;t update edit box when editing was canceled by ESC key (#2493935)</li>
<li>BugFix: Wrong number shown for difference when using line filters (#2493980)</li>
<li>BugFix: Crashed if compared file disappeared during file compare (#2493945)</li>
<li>BugFix: Creating new files caused message about missing files (#2515875)</li>
<li>BugFix: Could not hide folders in recursive compare (#2528749)</li>
<li>BugFix: Folder Compare:Copy Right/Left don&#39;t create folder</li>
<li>BugFix: Must Show Different to Show L/R Unique (#2129561)</li>
<li>BugFix: Could not copy files from right to left (#2556904)</li>
<li>BugFix: Don&#39;t show file name extension for folders (#2563314)</li>
<li>Translation updates
<ul>
<li>Brazilian (#2461443, #2524796)</li>
<li>Chinese Traditional (#2540115)</li>
<li>Czech (r6201)</li>
<li>Dutch (#2481022, #2494378, #2499994)</li>
<li>French (#2551043, #2551066)</li>
<li>Greek (#2550911)</li>
<li>Swedish (#2452467, #2465385, #2511043)</li>
</ul></li>
</ul>
<h2 id="winmerge-212x-r2_12-branch-created-r6202">WinMerge 2.12.x (R2_12) branch created (r6202)</h2>
<h2 id="winmerge-21118---2008-12-19-r6200">WinMerge ******** - 2008-12-19 (r6200)</h2>
<ul>
<li>Allow reloading the file if it has been changed on the disk behind WinMerge (#2354125)</li>
<li>Add option to show Open-dialog on WinMerge startup (#2327424)</li>
<li>Add Git to Source Control filter and fix ignore file rules (#2329381)</li>
<li>Add build identifier for 64-bit builds to About-dialog (r6142)</li>
<li>Installer: Option to add WinMerge to system path (#2435175)</li>
<li>Add Unicode version of heksedit (#2036603)</li>
<li>Allow multiple file compare documents by default (#2445749)</li>
<li>BugFix: Move confirmation dialog sometimes had wrong paths (#2327565, #2309190)</li>
<li>BugFix: Use translated version of readme.txt for Chinese Simplified (#2391000)</li>
<li>BugFix: Installer didn&#39;t install readme.txt for all translations (#2396437)</li>
<li>BugFix: Could not give paths to commandline when started WinMerge from Cygwin prompt (#2427946)</li>
<li>BugFix: Infinite loop in heksedit&#39;s Replace-dialog (r6176)</li>
<li>Translation updates
<ul>
<li>Croatian (#2390558)</li>
<li>German (r6130)</li>
<li>Greek (#2379356)</li>
<li>Japanese (r6154)</li>
<li>Swedish (#2318778, #2374278)</li>
</ul></li>
</ul>
<h2 id="winmerge-21117---2008-11-20-r6112">WinMerge ******** - 2008-11-20 (r6112)</h2>
<ul>
<li>Add shell context menu to folder compare context menu (#2189315)</li>
<li>Update PCRE to version 7.8 (#2209911)</li>
<li>Show missing lines in location pane for diffs having both different and missing lines (#2119235)</li>
<li>Syntax highlighting for Verilog files (#2231145)</li>
<li>Tweak new &quot;equal file&quot; icon a little bit (#2312381)</li>
<li>Readme update (#2234689)</li>
<li>Translation updates
<ul>
<li>Brazilian (#2219568)</li>
<li>Czech (r6034)</li>
<li>Swedish (#2215167, #2236651)</li>
</ul></li>
</ul>
<h2 id="winmerge-21116---2008-10-23-r6033">WinMerge 2.11.1.6 - 2008-10-23 (r6033)</h2>
<ul>
<li>General compare status for files (not known if text/binary) (#2175736)</li>
<li>Change icons for the new general compare status (#2185302)</li>
<li>Use 24bit icons for &quot;Bookmarks&quot; and &quot;Delete&quot; menu items (#2141184)</li>
<li>Installer: create backup of ClearCase config file (#2129095)</li>
<li>BugFix: Translatable strings having space before colon (#1971868)</li>
<li>BugFix: Wrong cursor position when disabling selection margin (#2138555)</li>
<li>BugFix: Showed a black line when disabling selection margin (#2068790)</li>
<li>BugFix: Fix manual URL in Readme.txt (#2181628)</li>
<li>BugFix: Long unique paths were mixed up in folder compare view (#2129634)</li>
<li>BugFix: Some calls made to plugins when plugins were disabled (#2182788)</li>
<li>BugFix: If messagebox was shown during file loading location pane didn&#39;t show any differences (#2101780)</li>
</ul>
<h2 id="winmerge-21115---2008-10-02-r5995">WinMerge 2.11.1.5 - 2008-10-02 (r5995)</h2>
<ul>
<li>Use external editor to edit file filters (#2082774)</li>
<li>Improved command line handling (#2081779)</li>
<li>Change Manual url to <a href="http://winmerge.org/docs/manual/">http://winmerge.org/docs/manual/</a> (r5956)</li>
<li>Manual updates</li>
<li>Plugins: Show processing instructions in XML plugin (#1979321)</li>
<li>Add icon for &quot;Compare&quot; menu item (#2119843)</li>
<li>Change download URL to current URL (#2136765)</li>
<li>Installer: create backup of ClearCase config file (#2129095)</li>
<li>BugFix: ClearCase checkout/commit dialog translation error (#2051069)</li>
<li>BugFix: Table of Contents in manual (#2071625)</li>
<li>BugFix: Highlight ShowUnInstDetails keyword for NSIS files (#2084879)</li>
<li>BugFix: Displayed a blank time when file last modified time is 00:00:00 (#2098626)</li>
<li>BugFix: Customized colors weren&#39;t used in some areas (#2110218)</li>
<li>BugFix: Zip files were extracted into wrong folder (#2110350)</li>
<li>BugFix: Case-sensitive regexp search didn&#39;t work (#1991259)</li>
<li>BugFix: There were some installer regressions (eg. Installer didn&#39;t create Quick Launch icon) (#1460517)</li>
<li>BugFix: Whitespace ignore options didn&#39;t work with quick compare (#2120174)</li>
<li>BugFix: Giving path to file and folder to command line did not work (#2138560)</li>
<li>New translation: Galician (#2120685)</li>
<li>Translation updates
<ul>
<li>German (r5934)</li>
<li>Russian (#2067785)</li>
<li>Swedish (#2075513)</li>
<li>Ukrainian (#2131753)</li>
</ul></li>
</ul>
<h2 id="winmerge-21114---2008-08-21-r5846">WinMerge ******** - 2008-08-21 (r5846)</h2>
<ul>
<li>Binary file edit/compare (#2036603)</li>
<li>Add wait cursor for undo and redo (#2039228)</li>
<li>Add icon for &quot;Open Conflict File&quot; menu item (#2047054)</li>
<li>Include unique subfolder contents in recursive folder compare (#2034259)</li>
<li>Installer: Use -u switch in TortoiseSVN command line (#2060782)</li>
<li>Add icon for &quot;Plugins-&gt;List&quot; menu item (#2060771)</li>
<li>BugFix: External editor command line switches did not work (#2037862)</li>
<li>BugFix: Deleting/Moving file crashed WinMerge in debugger (#2025833)</li>
<li>BugFix: Lost focus after folder compare operations (#2046002)</li>
<li>BugFix: Some files had folder icons when aborting folder compare (#2018836)</li>
<li>BugFix: Didn&#39;t select word-diff cyclically by pressing F4 key (#1989858)</li>
<li>BugFix: Could not reset EOL ignore after selecting it from query dialog (#2056741)</li>
<li>Translation updates
<ul>
<li>Brazilian (#2059836)</li>
<li>German (#2045666)</li>
<li>Greek (#2062442)</li>
<li>Chinese Traditional (#2039929)</li>
<li>Japanese (r5805)</li>
<li>Swedish (#2062107)</li>
</ul></li>
</ul>
<h2 id="winmerge-21113---2008-08-04-r5733">WinMerge 2.11.1.3 - 2008-08-04 (r5733)</h2>
<ul>
<li>Rename color scheme menu item &quot;None&quot; and add separator (#2027300)</li>
<li>Add -u commandline parameter as alternative to -ub (#2029275, #2030359)</li>
<li>Remove RCLocalizationHelper plugin (#2029457)</li>
<li>Clarify Supercopier caused hang in release notes (#2030040)</li>
<li>Improve tree mode in folder compare (#2018836)</li>
<li>Unlimited undo (#2033754)</li>
<li>BugFix: Could change syntax highlight mode when highlighting was disabled from options (#2027325)</li>
<li>BugFix: Editing line filters updated wrong filter (#2025130)</li>
<li>BugFix: Boldfacing did work only for Courier font (#2000996)</li>
<li>BugFix: Shift+Backspace didn&#39;t work in editor (#1153696)</li>
</ul>
<h2 id="winmerge-21112---2008-07-24-r5673">WinMerge 2.11.1.2 - 2008-07-24 (r5673)</h2>
<ul>
<li>Refresh compare after changing filter (#2017426)</li>
<li>Add dialog listing available plugins (#2021722)</li>
<li>Hierarchical directory comparison result view (#2018836)</li>
<li>Add &quot;Open Conflict&quot; for file/folder compare menus (#2025472)</li>
<li>Enable hierarchial folder compare view by default (#2025478)</li>
<li>Confirmation dialog for move operations (#2026508)</li>
<li>Manual: New build system (#2015434)</li>
<li>Manual: Improve Opening Files and Folders (#2017901)</li>
<li>Manual: Improve folder compare documentation (#2024587)</li>
<li>BugFix: Failed to compare UTF-8 files plugins disabled (#2018287)</li>
<li>BugFix: Don&#39;t warn about closing multiple windows when only one window is open (#2010463)</li>
<li>BugFix: Moving was allowed for protected items (#2023734)</li>
<li>BugFix: Could change EOL style for protected file (#2025781)</li>
</ul>
<h2 id="winmerge-21111---2008-07-11-r5609">WinMerge ******** - 2008-07-11 (r5609)</h2>
<ul>
<li>Manual: Introduction update (#2011365)</li>
<li>Manual: Reorganize structure (#2011924)</li>
<li>Add ShellExtension menu for drive letters (root folders) (#2011602)</li>
<li>Remove buildall.bat (#2013576)</li>
<li>New option to enable plugins (disabled by default) (#2013845)</li>
<li>Plugins: Show processing instructions in XML plugin (#1979321)</li>
<li>BugFix: Use system codepage for paths (#1979429, #2008581)</li>
<li>BugFix: ANSI build crash in folder compare (#2010053)</li>
<li>BugFix: Add insert/overtype mode indicator to statusbar (#2015174)</li>
<li>BugFix: Detected UTF-8 encoding shown as 65001 (#2012377)</li>
</ul>
<h2 id="winmerge-210x-r2_10-branch-created-r5561">WinMerge 2.10.x (R2_10) branch created (r5561)</h2>
<h2 id="winmerge-2915---2008-07-04-r5560">WinMerge 2.9.1.5 - 2008-07-04 (r5560)</h2>
<ul>
<li>Manual: Copy editing (#2000322, #2005418)</li>
<li>Manual: Clarify 64-bit support (#2002431)</li>
<li>Reduce executable sizes (#2003203)</li>
<li>Release Notes update (#2007673, r5557)</li>
<li>BugFix: Possible crash when enabling line filter (#2004160)</li>
<li>BugFix: Location Pane flickers when scrolling (#1938177, #2005031)</li>
<li>BugFix: Use system codepage for paths (#1979429, #2008581)</li>
<li>BugFix: Random crashes in folder compare (#2005308)</li>
<li>Translation updates
<ul>
<li>Chinese Traditional (#2007426)</li>
</ul></li>
</ul>
<h2 id="winmerge-2914---2008-06-24-r5518">WinMerge 2.9.1.4 - 2008-06-24 (r5518)</h2>
<ul>
<li>Use PCRE regular expressions in the editor (#1991259)</li>
<li>Installer: install to all users (#1460517)</li>
<li>Update release notes (#1990886, #1995265, #1999880)</li>
<li>Manual: Use PNG files (#1988845, #1989744)</li>
<li>BugFix: Temp paths in Reports created from archive file (#1984346)</li>
<li>BugFix: ISL file syntax highlighting starts comment with { (#1981326)</li>
<li>BugFix: Crash when starting a second instance (#1993835)</li>
<li>BugFix: Crash when starting WinMerge with only one path (#1994402)</li>
<li>BugFix: Messages missing from installer translations (r5506)</li>
<li>BugFix: Always right side as origin side in copy confirmation dialog when Copy To... (#2001237)</li>
<li>New translation: Slovenian (#1997236)</li>
<li>Translation updates
<ul>
<li>Brazilian (#1994578)</li>
<li>Swedish (#2000307)</li>
</ul></li>
</ul>
<h2 id="winmerge-2913---2008-06-05-r5438">WinMerge ******* - 2008-06-05 (r5438)</h2>
<ul>
<li>Update PCRE (regular expression library) to version 7.7 (#1941779)</li>
<li>Add VB.net keywords to Basic highlighter (#1970912)</li>
<li>Open conflict files by drag &amp; dropping to main window (#1984884)</li>
<li>Possible improvement for WinMerge exit bug (#1602313)</li>
<li>BugFix: Location Pane flickers when resizing (#1740106)</li>
<li>BugFix: Command line description text starting with &quot;/&quot; confused WinMerge (#1973225)</li>
<li>BugFix: Item count in statusbar not get cleared (#1976480)</li>
<li>New translation: Ukrainian (#1973149, #1974021)</li>
<li>Translation updates
<ul>
<li>Brazilian (#1978374)</li>
<li>Korean (#1978362)</li>
<li>Swedish (#1980970)</li>
</ul></li>
</ul>
<h2 id="winmerge-2912---2008-05-23-r5362">WinMerge ******* - 2008-05-23 (r5362)</h2>
<ul>
<li>Optimize location pane (r5341, r5342, r5346, r5351, r5355, r5356)</li>
<li>BugFix: Version Control filter did not match all VSS files (#1881186)</li>
<li>BugFix: Fix showing differences in the end of the line (#1883409, #1962816)</li>
<li>BugFix: Fix command line paths in quotes ending with &quot;&quot; (#1963523)</li>
<li>New translation: Greek (#1968429)</li>
<li>Translation updates
<ul>
<li>Bulgarian (#1957434)</li>
<li>Croatian (#1958283)</li>
<li>Swedish (#1950188, #1970074)</li>
</ul></li>
</ul>
<h2 id="winmerge-2911---2008-04-23-r5324">WinMerge ******* - 2008-04-23 (r5324)</h2>
<ul>
<li>Release Notes update (#1933950)</li>
<li>Installer: Use correct way to create start menu web link (#1913919)</li>
<li>Installer: Simplify DeletePreviousStartMenu() function (#1913949)</li>
<li>Installer: Updated the side art of the installer application (#1915012)</li>
<li>Update URLs and switch to 2.8 manual (#1919965)</li>
<li>New splash screen (#1914869)</li>
<li>Tab icons at Filter dialog (#1926531)</li>
<li>Add VS2003/VS2005 project files (#1926346)</li>
<li>Create separate document for compiling WinMerge (#1927502)</li>
<li>Update InnoSetup translations (#1931492)</li>
<li>Make &#39;Yes&#39; the default button in copy confirmation dialog (#1943647)</li>
<li>Allow drag &amp; drop of paths to Open dialog (#1945254)</li>
<li>Enlarge &#39;Discard All&#39; button in Save confirmation dialog (#1947216)</li>
<li>BugFix: Installer could create a registry access error (#1909967)</li>
<li>BugFix: The filter text box in Open-dialog sometimes displayed some garbage data (#1915424)</li>
<li>BugFix: Print area was not clipped correctly. (#1915536)</li>
<li>BugFix: Undo after pane swapping (#1915536, #1923625)</li>
<li>BugFix: Quick compare for same folder didn&#39;t show differences (#1918599)</li>
<li>BugFix: Installer didn&#39;t have ChangeLog.txt included (r5255)</li>
<li>BugFix: Shortcuts given in command line were not expanded (#1942217)</li>
<li>New translation: Romanian (#1939729)</li>
<li>Translation updates
<ul>
<li>Brazilian (#1913723)</li>
<li>Chinese Traditional (#1927605)</li>
<li>French (#1920380)</li>
<li>German (r5130, r5139, r5171, r5203, r5236, #1949068)</li>
<li>Italian (#1939235)</li>
<li>Japanese (r5152)</li>
<li>Polish (#1934655)</li>
<li>Russian (#1914466)</li>
<li>Spanish (#1907152, r5250)</li>
<li>Swedish (#1908289)</li>
</ul></li>
</ul>
<h2 id="winmerge-28-rc---2008-03-04-r5125">WinMerge 2.8 RC - 2008-03-04 (r5125)</h2>
<ul>
<li>Update developers list in splash screen (#1891548)</li>
<li>Better error handling for Excel plugin (#1510293)</li>
<li>Add macros, bookmarks and document properties to Word plugin (#1510298)</li>
<li>Add *.dot (document template) extension to Word plugin (r5120)</li>
<li>BugFix: Copy&amp;Paste from VB6 added binary chars (#1904355)</li>
<li>Translation updates
<ul>
<li>Chinese Traditional (#1905323)</li>
<li>Swedish (#1905520)</li>
</ul></li>
</ul>
<h2 id="winmerge-2776---2008-02-28-r5099">WinMerge 2.7.7.6 - 2008-02-28 (r5099)</h2>
<ul>
<li>Remove sintance.h/sintance.cpp as not compatible with GPL (#1886580)</li>
<li>Automatically switch to mixed-EOL mode when loading files with two or more EOL styles (#1901547)</li>
<li>Indent &quot;Explorer Context Menu&quot; sub options (#1901763)</li>
<li>Replace &#39;DOS&#39; in EOL style strings with &#39;Windows&#39; (#1902583)</li>
<li>Show &#39;Mixed&#39; as file compare EOL style instead of empty style (r5091)</li>
<li>Typo fixes and grammar improvements to several strings (#1898401)</li>
<li>BugFix: Ignore codepage specified on File Encoding dialog if file was detected as UTF-8 (#1900728)</li>
<li>BugFix: Recognize invalid UTF-8 sequence as UTF-8 (#1900733)</li>
<li>BugFix: Didn&#39;t highlight any difference between untitled documents (#1900257)</li>
<li>Translation updates
<ul>
<li>Czech (r5073)</li>
<li>Japanese (r5076)</li>
<li>Swedish (#1901784)</li>
</ul></li>
</ul>
<h2 id="winmerge-2775---2008-02-22-r5069">WinMerge 2.7.7.5 - 2008-02-22 (r5069)</h2>
<ul>
<li>Cleaning up Help-menu (#1875111)</li>
<li>Right-align tab size number in editor options (r5037)</li>
<li>Move Time difference ignore -option to compare options (#1892283)</li>
<li>Add option to select temporary folder (#1893167)</li>
<li>Show file encoding in file compare statusbar (#1895629)</li>
<li>Unify EOL style strings in folder compare and file compare (#1896462)</li>
<li>Remove &quot;DisplayBinaryFiles&quot; and &quot;EditBinaryFiles&quot; plugin (#1899161)</li>
<li>BugFix: Potential lockup in folder compare (#1865131, #1889907)</li>
<li>BugFix: Line filter didn&#39;t filter non-ascii lines correctly (#1880628)</li>
<li>BugFix: GNU General Public License name in menu (#1868989)</li>
<li>BugFix: Didn&#39;t switch back to content compare once switched to quick compare in folder compare (#1770373)</li>
<li>BugFix: Quick compare didn&#39;t ignore EOL differences (#1884717)</li>
<li>Translation updates
<ul>
<li>Croatian (r5032)</li>
<li>Czech (r5017)</li>
<li>German (r5038)</li>
<li>Slovak (#1895583)</li>
<li>Swedish (#1891326)</li>
</ul></li>
</ul>
<h2 id="winmerge-2774---2008-02-07-r5011">WinMerge 2.7.7.4 - 2008-02-07 (r5011)</h2>
<ul>
<li>Detect (and read/write) UTF-8 files without BOM (#1879271)</li>
<li>Tell user that file is not a conflict file (#1880423)</li>
<li>Allow opening conflict file from command line (#1880857)</li>
<li>Use radio symbol for current EOL menu item (#1869755)</li>
<li>Ask and create pair for unique folder and open them (#1881454)</li>
<li>Add publisher info to version resource (#1884920)</li>
<li>Move &quot;Zoom In/Out&quot; to own sub menu (#1879340)</li>
<li>Installer: Add more version resource info (#1885793)</li>
<li>Reset text zoom to default size with Ctrl + * (#1886664)</li>
<li>BugFix: Always return 0 to command line (#1854419)</li>
<li>BugFix: Merge/Delete deleted items from read-only sides (#1882019)</li>
<li>BugFix: Shellextension had no publisher info and wrong description (#1878828)</li>
<li>BugFix: Lockup when showing binary files (#1880711)</li>
<li>Translation updates
<ul>
<li>Croatian (r4952)</li>
<li>Japanese (r4962)</li>
<li>Swedish (#1884918)</li>
</ul></li>
</ul>
<h2 id="winmerge-2773---2008-01-23-r4951">WinMerge ******* - 2008-01-23 (r4951)</h2>
<ul>
<li>Resolve conflict files (cvs, svn...) (#1875129)</li>
<li>Save line filter when edit box loses focus (#1875853)</li>
<li>Add link to translations website at help menu (#1699883)</li>
<li>Manual: Add instructions for conflict file resolving (#1876770)</li>
<li>Manual: Document third path given to command line (#1877735)</li>
<li>BugFix: Fix Quick Compare regression in ******* (#1872165)</li>
<li>BugFix: Clipped text in file saving error message (#1874854)</li>
</ul>
<h2 id="winmerge-2772---2008-01-14-r4915">WinMerge ******* - 2008-01-14 (r4915)</h2>
<ul>
<li>Syntax highlighting for PowerShell files (#1859657)</li>
<li>Syntax highlighting for PO(T) files (#1866221)</li>
<li>Change font size with Ctrl + MouseWheel (#1865795, #1869686)</li>
<li>Horizontal scrolling with Shift + MouseWheel (#1865795)</li>
<li>Installer: Add support for integration with latest version of TortoiseCVS and TortoiseSVN x64 (#1865168)</li>
<li>Close compare tab with middle mouse button (#1865220)</li>
<li>Add index.html for documentation folders (#1866183)</li>
<li>Manual: Clarify file filters folder name matching (#1867212)</li>
<li>Improve file compare margin icons location (#1718699)</li>
<li>Enable file compare to open binary files (#1869647)</li>
<li>File encoding dialog for file compare (#1871079)</li>
<li>BugFix: Crash when using TrackPoint middle-button scrolling (#1864040)</li>
<li>BugFix: WinMerge didn&#39;t show the contents of the symbolic link target (#1864978)</li>
<li>BugFix: Unused registry key HKCU\Thingamahoochie\WinMerge was created (#1865202)</li>
<li>BugFix: Register.bat didn&#39;t work on Windows 98 and Vista (#1869821)</li>
<li>BugFix: Open-dialog was not displayed on Windows 98 (#1866442, #1869858)</li>
<li>BugFix: Right mouse button didn&#39;t move cursor to new place (#1814184)</li>
<li>Translation updates
<ul>
<li>Spanish (#1867234)</li>
<li>Swedish (#1867765, #1867844)</li>
</ul></li>
</ul>
<h2 id="winmerge-2771---2007-12-20-r4842">WinMerge 2.7.7.1 - 2007-12-20 (r4842)</h2>
<ul>
<li>New option to keep file&#39;s time when saving in file compare (#1851921)</li>
<li>Installer: Link to translated ReadMe in Start menu, if file exists (#1805818)</li>
<li>Add HTML-formatted release notes (#1851308, #1852534)</li>
<li>Installer: Install release notes file (#1852599)</li>
<li>Installer: Move manual to core files component and remove</li>
<li>User Guide component (#1853409)</li>
<li>BugFix: VS2008 and VS2005 compile problems (#1847265)</li>
<li>BugFix: More difference highlight code reverts (#1748940)</li>
<li>Translation updates
<ul>
<li>Spanish (#1655577)</li>
</ul></li>
</ul>
<h2 id="winmerge-276---2007-11-29-r4807">WinMerge 2.7.6 - 2007-11-29 (r4807)</h2>
<ul>
<li>Touchpad scrolling improvements (#1837457)</li>
<li>BugFix: Values with &quot;,&quot; inside could break the CSV report (#1831512)</li>
<li>BugFix: Failed to create backup file in folder compare (#1835283)</li>
<li>BugFix: Revert difference highlight code to 2.6.12 version (#1811695)</li>
<li>BugFix: Backspace key didn&#39;t work when renaming items (#1738790)</li>
<li>Translation updates
<ul>
<li>Croatian (r4786)</li>
</ul></li>
</ul>
<h2 id="winmerge-2757---2007-11-19-r4772">WinMerge ******* - 2007-11-19 (r4772)</h2>
<ul>
<li>Remove MFC dependency from ShellExtension (#1833521)</li>
<li>Update ShellExtension icon to new WinMerge icon (#1833616)</li>
<li>BugFix: Build script didn&#39;t update PO files correctly before copying (r4756)</li>
<li>BugFix: ShellExtension&#39;s icon was clipped if system font size was small (#1833616)</li>
<li>BugFix: Merge controls were not enabled for missing lines (#1833714)</li>
</ul>
<h2 id="winmerge-2756---2007-11-15-r4752">WinMerge ******* - 2007-11-15 (r4752)</h2>
<ul>
<li>BugFix: Many translation-system related fixes and improvements (#1804762)</li>
<li>BugFix: When copying files to VCS system, destination file was not checked out (#1828930)</li>
<li>BugFix: Visible area indicator on location pane shook when clicking above or below the vertical scroll bar to scroll one screen (#1829572)</li>
<li>Translation updates
<ul>
<li>Croatian (#1829301)</li>
<li>Japanese (r4730)</li>
</ul></li>
</ul>
<h2 id="winmerge-2755---2007-11-08-r4722">WinMerge ******* - 2007-11-08 (r4722)</h2>
<ul>
<li>BugFix: Don&#39;t enable merge GUI for ignored differences (#1826375)</li>
<li>BugFix: PgUp key caused the error message &quot;An invalid argument was encountered&quot; when word-wrap mode was on (#1820631)</li>
<li>BugFix: Keep selected items visible in Folder Compare columns dialog (r4715)</li>
<li>BugFix: Disable Folder Compare column selection dialog&#39;s up/down buttons when first/last items are selected (r4716)</li>
<li>BugFix: Many translation-system related fixes and improvements (#1804762)</li>
<li>Add Croatian translation (#1820308)</li>
<li>Translation updates
<ul>
<li>Chinese Traditional (#1824691)</li>
</ul></li>
</ul>
<h2 id="winmerge-2754---2007-11-01-r4687">WinMerge ******* - 2007-11-01 (r4687)</h2>
<ul>
<li>New PO files based translation system (#1804762)</li>
<li>New folder compare column selection dialog (#1804555)</li>
<li>Install WinMerge.exe (ANSI version) only for Windows 95/98/ME (#1809140)</li>
<li>Better multi-monitor support, with option to lock panes (#1788168)</li>
<li>Installer: Install PO files and MergeLang.dll (#1820689)</li>
<li>BugFix: Ignored differences were merged when merging multiple differences (#1811745)</li>
<li>BugFix: Don&#39;t clear selection when right-clicking outside it (#1814184)</li>
<li>BugFix: Configuration log labeled Vista as Longhorn (#1815859)</li>
<li>BugFix: Customized text color for ignored differences didn&#39;t shown (#1807895)</li>
<li>Translation updates
<ul>
<li>Chinese Traditional (#1810192, #1810541)</li>
<li>Italian (#1805044)</li>
</ul></li>
</ul>
<h2 id="winmerge-2753---2007-09-27-r4572">WinMerge ******* - 2007-09-27 (r4572)</h2>
<ul>
<li>Use PO files for translations (generate RC-files from PO-files)</li>
<li>Add Portuguese translation (#1756364)</li>
<li>Change Up/Down button names in folder compare columns dialog(#1800064)</li>
<li>BugFix: Fix MakeResDll crash (#1795421)</li>
<li>BugFix: Full screen mode didn&#39;t work with multiple monitors (#1788168)</li>
<li>BugFix: Revert multiple monitor patches (#1788168)</li>
<li>BugFix: PgUp key didn&#39;t do anything in beginning of the file (#1795680)</li>
<li>BugFix: The option &#39;Automatically scroll to first difference&#39; didn&#39;t work properly when word-wrap mode was on (#1795814)</li>
<li>Translation updates
<ul>
<li>French (#1800064, #1802363)</li>
<li>Japanese (r4546)</li>
</ul></li>
</ul>
<h2 id="winmerge-2752---2007-09-11-r4528">WinMerge ******* - 2007-09-11 (r4528)</h2>
<ul>
<li>Graphical improvements to location pane (#1770175)</li>
<li>Remove MFC dependency from resource compiler (#1783581)</li>
<li>Better multi-monitor support (split file view on monitor boundary) (#1788168)</li>
<li>BugFix: Renaming unique item (only other side) renamed parent folder (#1787816)</li>
<li>BugFix: Goto didn&#39;t change the active pane when selecting another pane (#1791351)</li>
<li>Translation updates</li>
<li>Traditional Chinese (r4473)</li>
</ul>
<h2 id="winmerge-2751---2007-08-16-r4440">WinMerge ******* - 2007-08-16 (r4440)</h2>
<ul>
<li>HTML reports for file compare (#1739209)</li>
<li>Ask confirmation when exiting with multiple windows open (#1757800)</li>
<li>Translation updates
<ul>
<li>Catalan (r4370)</li>
<li>Simplified Chinese (r4369)</li>
<li>Danish (r4360)</li>
<li>French (r4383)</li>
<li>Russian (r4421)</li>
<li>Slovak (r4413)</li>
<li>Swedish (r4412)</li>
</ul></li>
</ul>
<h2 id="winmerge-274---2007-06-27-r4352">WinMerge 2.7.4 - 2007-06-27 (r4352)</h2>
<ul>
<li>Batch-file syntax highlighting keywords cleanup (#1743504)</li>
<li>BugFix: Archivesupport Zip--&gt;both didn&#39;t work correctly (#1734439)</li>
<li>BugFix: Linediff didn&#39;t highlight whitespace differences (#1698781)</li>
<li>BugFix: Copy confirmation dialog did show wrong To-path (#1740024)</li>
<li>Translation updates
<ul>
<li>Italian (#1737491)</li>
</ul></li>
</ul>
<h2 id="winmerge-2737---2007-06-14-r4330">WinMerge ******* - 2007-06-14 (r4330)</h2>
<ul>
<li>New big toolbar (actual image instead of placeholder) (#1736520)</li>
<li>BugFix: Fixes to folder compare copy/rename operations (#1730534)</li>
<li>Translation updates
<ul>
<li>Swedish (#1735635)</li>
</ul></li>
</ul>
<h2 id="winmerge-2736---2007-06-07-r4321">WinMerge ******* - 2007-06-07 (r4321)</h2>
<ul>
<li>Recent project files in File-menu (#1731551)</li>
<li>Automatically import existing linefilters to new dialog (#1718040)</li>
<li>BugFix: copy/delete of unique items failed in folder compare in ******* (#1730534)</li>
<li>BugFix: crashed when ESC was pressed while renaming item in folder compare (#1705874)</li>
</ul>
<h2 id="winmerge-2735---2007-05-31-r4304">WinMerge ******* - 2007-05-31 (r4304)</h2>
<ul>
<li>Relax selecting parent folder restrictions (up arrow in folder compare) (#1727829)</li>
<li>Show a warning if item can&#39;t be found anymore in folder compare and it is tried to access (copy/move/delete) (#1723778)</li>
<li>New Borland Delphi file filter (#1699783)</li>
<li>Gradient toolbar (#1717774)</li>
<li>BugFix: could not create new file filters in 2.7.3.4 (#1719892)</li>
</ul>
<h2 id="winmerge-2734---2007-05-15-r4287">WinMerge 2.7.3.4 - 2007-05-15 (r4287)</h2>
<ul>
<li>Big (32x32) toolbar (image still placeholder) (#1698641)</li>
<li>Improve folder compare copy confirmation dialog (#1699585, #1700241)</li>
<li>New file filter for Symbian developers (#1694048)</li>
<li>BugFix: several fixes to in-line difference highlight (#1694102, #1714088)</li>
<li>BugFix: word wrap lose horizontal scrollbar (#1706476)</li>
<li>BugFix: fixes to tabbed window control (#1718148)</li>
<li>Translation updates
<ul>
<li>German (#1701151)</li>
<li>Japanese</li>
<li>Korean (#1704904)</li>
<li>Swedish (#1703350)</li>
</ul></li>
</ul>
<h2 id="winmerge-2733---2007-04-04-r4234">WinMerge 2.7.3.3 - 2007-04-04 (r4234)</h2>
<ul>
<li>Tabbed interface (#1603292)</li>
<li>Enable shell integration for folder backgrounds (#1693113)</li>
<li>Allow selecting syntax highlighting (#1690346)</li>
<li>New C# development file filter (#1689854)</li>
<li>Icon updates (#1686279, #1692097)</li>
<li>BugFix: compare by date and by size broken in 2.7.3.2 (#1693441)</li>
<li>BugFix: Open-dialog file filter failed if modified (#1693275)</li>
<li>Translation updates
<ul>
<li>Swedish (#1689515)</li>
</ul></li>
</ul>
<h2 id="winmerge-2732---2007-03-27-r4201">WinMerge 2.7.3.2 - 2007-03-27 (r4201)</h2>
<ul>
<li>Several fixes to line difference highlighting (#1491334, #1683061, #1639453)</li>
<li>Icon updates (#1684865)</li>
<li>Printing improvement (#1687430)</li>
<li>Language files size reduced (#1687661, #1687983, #1688012)</li>
<li>BugFix: 2.7.3.1 binary file compare crash (#1687966)</li>
<li>BugFix: long linefilters now restored correctly (#1680920)</li>
<li>Translation updates
<ul>
<li>Japanese</li>
</ul></li>
</ul>
<h2 id="winmerge-2731---2007-03-20-r4179">WinMerge 2.7.3.1 - 2007-03-20 (r4179)</h2>
<ul>
<li>New copy confirmation dialog in folder compare (#1675087, #1683842)</li>
<li>Improved line filter dialog (#1682475)</li>
<li>Installer/uninstaller icons restored to default icons</li>
<li>Some icon updates (#1680209)</li>
<li>BugFix: folder compare lost focus after confirmation dialog (#1670991)</li>
<li>BugFix: crash comparing empty folders (#1675211)</li>
<li>Translation updates
<ul>
<li>Czech</li>
<li>French (#1664689)</li>
<li>Polish (#1673082)</li>
<li>Swedish (#1673908, #1678676, #1683289)</li>
</ul></li>
</ul>
<h2 id="winmerge-272---2007-02-27-r4137">WinMerge 2.7.2 - 2007-02-27 (r4137)</h2>
<ul>
<li>Fix ShellExtension installation bug</li>
<li>Translation updates
<ul>
<li>Czech</li>
<li>German (#1667564)</li>
<li>French (#1664689)</li>
</ul></li>
</ul>
<h2 id="winmerge-2717---2007-02-20-r4120">WinMerge 2.7.1.7 - 2007-02-20 (r4120)</h2>
<ul>
<li>Folder compare threading improvement - should be faster in many situations (#1662002)</li>
<li>New options for backup file location and naming (#1652696)</li>
<li>Syntax highlighting for CSS files (#1582537)</li>
<li>Allow to hide folder compare margins (View/View Margins) (#1663798)</li>
<li>Translation updates
<ul>
<li>Czech</li>
<li>Polish (#1650831)</li>
<li>Swedish (#1657664)</li>
</ul></li>
</ul>
<h2 id="winmerge-2716---2007-02-01-r4094">WinMerge 2.7.1.6 - 2007-02-01 (r4094)</h2>
<ul>
<li>New post-compare line filters for folder compare (#1644820)</li>
<li>Optimization for word wrap code (#1640741)</li>
<li>BugFix: 2.7.1.5 forgets selected filter (#1637433)</li>
<li>BugFix: limiting to single file compare window now works more logically (#1636314)</li>
<li>BugFix: ANSI regular expressions didn&#39;t work (#1644668)</li>
<li>Translation updates
<ul>
<li>Bulgarian (#1639493)</li>
<li>Catalan (#1646638)</li>
<li>Swedish (#1634968)</li>
</ul></li>
</ul>
<h2 id="winmerge-2715---2007-01-10-r4030">WinMerge 2.7.1.5 - 2007-01-10 (r4030)</h2>
<ul>
<li>New line filter implementation (please test and report bugs!) (#1593810)</li>
<li>Minimum height for location pane&#39;s visible area (#1489875)</li>
<li>Short label &quot;Error&quot; for error items in folder compare (#1567749)</li>
<li>BugFix: rename edits wrong field in folder compare (#1597939)</li>
<li>BugFix: too much scrolling in difference navigation</li>
<li>BugFix: wrong text selection after pane switch in file compare (#1630630)</li>
<li>BugFix: crash when editing space-char indented files (#1631613)</li>
<li>Manual updates</li>
</ul>
<h2 id="winmerge-2714---2006-12-21-r3991">WinMerge 2.7.1.4 - 2006-12-21 (r3991)</h2>
<ul>
<li>Match lines inside differences (#1447744)</li>
<li>Workaround-patch (locally included to the build) for shutdown problems (#1602313)</li>
<li>New ASP.Net filefilter (#1619689)</li>
<li>BugFix: folder compare speed regression in ******* (#1610442)</li>
<li>BugFix: new fix for folder compare Copy Left/Right To.. (#1603061)</li>
<li>BugFix: copyright info missing from about dialog when translation selected (#1604115)</li>
<li>BugFix: location pane problems with word-wrap (#1584068, #1611542)</li>
<li>BugFix: invalid path added to project file -dialog (#1602219)</li>
<li>Swedish translation update (#1614442)</li>
<li>Manual updates</li>
</ul>
<h2 id="winmerge-2713---2006-12-05-r3919">WinMerge ******* - 2006-12-05 (r3919)</h2>
<ul>
<li>Now hides skipped items by default (#1604078)</li>
<li>Improved INI file syntax highlighting (#1607193)</li>
<li>Easier folder selection in Project File -dialog (#1603196)</li>
<li>New &quot;Exclude Source Control files and directories filter&quot; filefilter (#1557295)</li>
<li>BugFix: project files weren&#39;t loaded correctly from command line (using shell file association) (#1602214)</li>
<li>BugFix: crash in syntax parser (#1556688)</li>
<li>BugFix: wrong difference navigation when word-wrap enabled (#1597814)</li>
<li>BugFix: ClearCase integration didn&#39;t install if &quot;Program Files&quot; -folder was named differently (non-English Windows)</li>
<li>Manual updates
<ul>
<li>Slovak translation update (#1606496)</li>
</ul></li>
</ul>
<h2 id="winmerge-2712---2006-11-24-r3844">WinMerge ******* - 2006-11-24 (r3844)</h2>
<ul>
<li>close WinMerge if Open-dialog is canceled when started from command line (#1600714)</li>
<li>BugFix: project files didn&#39;t work (loaded right-side path for both sides) (#1600000)</li>
<li>BugFix: location pane misdrawn when word-wrap enabled (#1584068)</li>
</ul>
<h2 id="winmerge-2711---2006-11-16-r3810">WinMerge 2.7.1.1 - 2006-11-16 (r3810)</h2>
<ul>
<li>Use expat + scew for XML handling (partially converted) (1587574)</li>
<li>Uses PCRE for regular expressions (partially converted) (#1591605)</li>
<li>New folder compare icons (#1586705)</li>
<li>New bookmarks icons (#1586689)</li>
<li>Manifest files in resource, no need for separate files (#1588212)</li>
<li>BugFix: WinMerge/Filters folder was always created (#1578419)</li>
<li>BugFix: modality problems in file selection dialogs (#1586869)</li>
<li>BugFix: crash if file modification time was missing (#1589563)</li>
<li>BugFix: /x parameter didn&#39;t work with some other parameters (#1564283)</li>
<li>BugFix: asked sometimes twice about saving modified files (#1596692)</li>
</ul>
<p>For older changes look at the VCS (Version Control System).</p>
</body>
</html>
