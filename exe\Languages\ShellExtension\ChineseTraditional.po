# This file is part from WinMerge <https://winmerge.org/>
# Released under the "GNU General Public License"
#
msgid ""
msgstr ""
"Project-Id-Version: WinMerge Shell Extension\n"
"Report-Msgid-Bugs-To: https://bugs.winmerge.org/\n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Last-Translator: SiderealArt <<EMAIL>>\n"
"Language-Team: ChineseTraditional <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Poedit-Basepath: ../../ShellExtension/Languages\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"Language: zh_TW\n"
"X-Generator: Poedit 3.0\n"
"X-Poedit-SourceCharset: UTF-8\n"

#. AFX_TARG_*
#: ShellExtension.rc:19
#, c-format
msgid "AFX_TARG_ENU"
msgstr "AFX_TARG_CHT"

#. LANGUAGE, SUBLANGUAGE
#: ShellExtension.rc:20
#, c-format
msgid "LANG_ENGLISH, SUBLANG_ENGLISH_US"
msgstr "LANG_CHINESE, SUBLANG_CHINESE_TRADITIONAL"

#: ShellExtension.rc:111
#, c-format
msgid "ShellExtension"
msgstr "系統介面延伸"

#: ShellExtension.rc:112
#, c-format
msgid "Win&Merge"
msgstr "Win&Merge"

#: ShellExtension.rc:113
#, c-format
msgid "Compare &As"
msgstr ""

#: ShellExtension.rc:114
#, c-format
msgid "&Compare"
msgstr "比較 (&C)"

#: ShellExtension.rc:115
#, c-format
msgid "Compare&..."
msgstr "比較&..."

#: ShellExtension.rc:116
#, c-format
msgid "Select &Left"
msgstr "選擇左側 (&L)"

#: ShellExtension.rc:117
#, c-format
msgid "Select &Middle"
msgstr "選擇中間 (&M)"

#: ShellExtension.rc:118
#, c-format
msgid "Re-select &Left"
msgstr "重新選擇左側 (&L)"
