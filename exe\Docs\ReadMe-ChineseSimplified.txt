﻿WINMERGE

WinMerge 是一款开源的运行于 Windows 平台的比较和合并工具。WinMerge 能够对文件夹
和文件进行比较，并以一种容易理解和处理的方式将差异进行呈现。WinMerge 既可以用做
外部的比较/合并工具，也可以单独使用。

WinMerge 有很多可以让比较、同步和合并尽可能简单易用的功能。也支持对多种编程语言
和文件格式进行语法高亮显示。

最新的 WinMerge 版本以及其它相关信息可以从 https://winmerge.org 得到。

WinMerge 快速入门
=================
要了解 WinMerge 安装好之后的基本操作，请点击菜单中的“帮助->WinMerge 帮助”并跳转
到 Quick start 章节。或者，也可以直接访问其在线版本：
https://manual.winmerge.org/Quick_start.html


WinMerge 手册
=============
WinMerge 的手册会以微软的 HTML 帮助文件的格式安装到本地，文件名为 WinMerge.chm。
可以点击菜单中的“帮助->WinMerge 帮助”或者在 WinMerge 窗口中按 F1 来打开手册。
如果是命令行，可以在运行 WinMerge 的可执行文件时加上“/?”参数来打开。

您也可以直接访问其在线版本：https://manual.winmerge.org/

WinMerge 支持
=============
如果您有 WinMerge 相关的问题或建议，请访问 WinMerge 的社区论坛：
https://github.com/WinMerge/winmerge/discussions
开发人员将阅读并回复论坛里的问题。

问题和功能需求
==============
如果问题没有在 WinMerge 论坛得到解决，可以在这里浏览和提交问题：
https://github.com/WinMerge/winmerge/issues

提交问题时，请在问题报告中包含 WinMerge 的版本号。你还可以通过点击菜单中的“帮助
->配置”来生成一份配置日志并将其以附件形式添加到问题报告中，其中包含了许多对开发
人员有用的信息。


- WinMerge 开发人员
