@echo off
setlocal EnableDelayedExpansion
set TikaVer=2.6.0
set TikaJar=tika-app-%TikaVer%.jar
set DOWNLOAD_URL=https://repo1.maven.org/maven2/org/apache/tika/tika-app/%TikaVer%/%TikaJar%
set TIKA_PATH=Commands\Apache-Tika\%TikaJar%
set MESSAGE='Apache Tika is not installed. Do you want to download it from %DOWNLOAD_URL%'
set TITLE='Apache Tika Plugin'
set TIKA_SHA256=fa289b58a5c1bb531ace78324625512a9448aa8472b5eb88b65988964048815a

cd "%APPDATA%\WinMerge"
if not exist %TIKA_PATH% (
  cd "%~dp0..\.."
  if not exist %TIKA_PATH% (
    mkdir "%APPDATA%\WinMerge" 2> NUL
    cd "%APPDATA%\WinMerge"
    for %%i in (%TIKA_PATH%) do mkdir %%~pi 2> NUL
    powershell "if ((New-Object -com WScript.Shell).Popup(%MESSAGE%,0,%TITLE%,1) -ne 1) { throw }" > NUL
    if errorlevel 1 (
      echo "download is canceled" 1>&2
    ) else (
      start "Downloading..." /WAIT powershell -command "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri %DOWNLOAD_URL% -UseBasicParsing -Outfile %TIKA_PATH%"
      powershell -command "$(CertUtil -hashfile '%TIKA_PATH%' SHA256)[1] -replace ' ','' -eq '%TIKA_SHA256%'" | findstr True > NUL
      if errorlevel 1 (
        echo %TIKA_PATH%: download failed 1>&2
        del %TIKA_PATH% 2> NUL
      )
    )
  )
)
"%~dp0..\Java\java.bat" -jar "%CD%\%TIKA_PATH%" %3 %4 %5 %6 %7 %8 %9 "%~1" > "%~2"
