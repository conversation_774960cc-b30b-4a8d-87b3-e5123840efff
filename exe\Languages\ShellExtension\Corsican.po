# This file is part from WinMerge <https://winmerge.org/>
# Released under the "GNU General Public License"
#
# Maintainer:
# * Patriccollu di Santa Maria è <PERSON> <patriccollu at gmail.com>
#
# Translators:
# * Patriccollu di Santa Maria è Si<PERSON>è, 2021-2022
#
msgid ""
msgstr ""
"Project-Id-Version: WinMerge in Corsican\n"
"Report-Msgid-Bugs-To: https://bugs.winmerge.org/\n"
"POT-Creation-Date: 2023-02-19 00:07+0000\n"
"PO-Revision-Date: 2023-03-12 14:56+0100\n"
"Language-Team: Patriccollu di Santa Maria è Si<PERSON>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-Basepath: ../../ShellExtension/Languages\n"
"X-Generator: Poedit 3.1.1\n"
"Last-Translator: Patriccollu di <PERSON> Maria è Si<PERSON>è <https://github.com/Patriccollu/Lingua_Corsa-Infurmatica/#readme>\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"Language: co\n"

#. AFX_TARG_*
#: ShellExtension.rc:19
#, c-format
msgid "AFX_TARG_ENU"
msgstr "AFX_TARG_COS"

#. LANGUAGE, SUBLANGUAGE
#: ShellExtension.rc:20
#, c-format
msgid "LANG_ENGLISH, SUBLANG_ENGLISH_US"
msgstr "LANG_CORSICAN, SUBLANG_DEFAULT"

#: ShellExtension.rc:111
#, c-format
msgid "ShellExtension"
msgstr "Estensione di u listinu cuntestuale"

#: ShellExtension.rc:112
#, c-format
msgid "Win&Merge"
msgstr "Win&Merge"

#: ShellExtension.rc:113
#, c-format
msgid "Compare &As"
msgstr "Paragunà &cum’è"

#: ShellExtension.rc:114
#, c-format
msgid "&Compare"
msgstr "&Paragunà"

#: ShellExtension.rc:115
#, c-format
msgid "Compare&..."
msgstr "&Paragunà…"

#: ShellExtension.rc:116
#, c-format
msgid "Select &Left"
msgstr "Selezziunà à &manca"

#: ShellExtension.rc:117
#, c-format
msgid "Select &Middle"
msgstr "Selezziunà à u &centru"

#: ShellExtension.rc:118
#, c-format
msgid "Re-select &Left"
msgstr "Riselezziunà à &manca"
