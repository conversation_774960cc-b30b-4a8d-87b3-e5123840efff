# This file is part from WinMerge <https://winmerge.org/>
# Released under the "GNU General Public License"
#
# Translators:
# * <PERSON> <michele.merega at ifminfomaster.com>
# * <PERSON> <deco1985 at hotmail.com>
# * <PERSON> <antonio.angelo at gmail.com>
# * <PERSON> <michele at locati.it>
# * bovirus <bovirus at gmail.com>
msgid ""
msgstr ""
"Project-Id-Version: WinMerge Shell Extension (08.03.2024)\n"
"Report-Msgid-Bugs-To: https://bugs.winmerge.org/\n"
"POT-Creation-Date: 2019-07-09 07:44+0000\n"
"PO-Revision-Date: 2024-03-09 11:02+0100\n"
"Last-Translator: bovirus <<EMAIL>>\n"
"Language-Team: Italian\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 3.4.2\n"
"X-Poedit-SourceCharset: UTF-8\n"

#. AFX_TARG_*
#: ShellExtension.rc:19
#, c-format
msgid "AFX_TARG_ENU"
msgstr "AFX_TARG_ITA"

#. LANGUAGE, SUBLANGUAGE
#: ShellExtension.rc:20
#, c-format
msgid "LANG_ENGLISH, SUBLANG_ENGLISH_US"
msgstr "LANG_ITALIAN, SUBLANG_ITALIAN"

#: ShellExtension.rc:111
#, c-format
msgid "ShellExtension"
msgstr "Estensione shell"

#: ShellExtension.rc:112
#, c-format
msgid "Win&Merge"
msgstr "Win&Merge"

#: ShellExtension.rc:113
#, c-format
msgid "Compare &As"
msgstr "Comp&ara come"

#: ShellExtension.rc:114
#, c-format
msgid "&Compare"
msgstr "&Compara"

#: ShellExtension.rc:115
#, c-format
msgid "Compare&..."
msgstr "Compara&..."

#: ShellExtension.rc:116
#, c-format
msgid "Select &Left"
msgstr "Se&leziona sinistra"

#: ShellExtension.rc:117
#, c-format
msgid "Select &Middle"
msgstr "Sele&ziona centrale"

#: ShellExtension.rc:118
#, c-format
msgid "Re-select &Left"
msgstr "Ri-se&leziona sinistra"
