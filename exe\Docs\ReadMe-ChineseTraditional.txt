﻿WINMERGE

WinMerge 是 Windows 作業系統下的、開放源碼的比較與合併的工具。WinMerge 能比較資料夾與檔案，以直接看到原文的易懂易處理的方式，呈現比較出的差異。WinMerge 可以獨立運用，也可以當成某個比較差異/合併工具的外部編輯器。

WinMerge 有許多有用的功能使得比較、同化、合併更加容易。許多程式語言和檔案格式都以語法高亮度顯示。

WinMerge 最新版與資訊在：https://winmerge.org/

立即上手
===========
安裝後，學習基本操作，按 說明 > WinMerge 說明。瀏覽 Quick Start 章。或至 https://manual.winmerge.org/Quick_start.html 瀏覽其網路版。

WinMerge 說明
============= 
安裝 WinMerge 時，其說明檔在地安裝成 Microsoft HTML Help 檔 ( WinMerge.chm)。開啟說明檔，按：Help > WinMerge 說明。或於WinMerge 窗，按 F1。於命令列，則以 "/?" 參數執行 WinMerge 的可執行檔。

您也可以到 https://manual.winmerge.org/ 瀏覽其HTML 版。

WinMerge 支援
================
有關於 WinMerge 的問題或建議嗎？請到 https://forums.winmerge.org/ 的 WinMerge 社群討論區。研發者常會閱讀與回應兩個討論區中的問題。一般性的議題，像使用上的問題，請到公開討論區 (Open Discussion forum)。研發上的議題，請到研發討論區 (Developers forum)。

程式錯誤與功能增益
=========================
若一個議題在討論區中未獲解決，可到計畫 ( https://project.winmerge.org/) 中的追蹤器 (Tracker) 查看，並按其選單的連結 (譬如，Bugs 或 Feature Requests)，到那裡就可以瀏覽或提出您的想法。

提報錯誤時請附上所用的 WinMerge 版本編號。按 說明 > 組態，可產生組態紀錄檔。將此檔附於您的錯誤提報；該檔有許多有用的資訊給研發者。


WinMerge 研發團隊

 	  	 
