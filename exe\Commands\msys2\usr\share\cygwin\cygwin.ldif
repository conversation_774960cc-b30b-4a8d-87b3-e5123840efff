# =========================================================================
#
# Schema Extension for Cygwin User and Group auxiliary classes
#
# Extend your Active Directory using
#
# ldifde -i -f <path>\<this>.ldif -b <username> <domain> <password> \
#   -k -c "CN=schema,CN=Configuration,DC=X" #schemaNamingContext
#
# Remember:
# - you have to be schema admin for your active directory
# - you have to run the above command directly from your schema master
#
# For further information read
#   http://technet.microsoft.com/en-us/magazine/2008.05.schema.aspx
#
# ----------------------------------------------------------------------
#
# Explanation for the OIDs:
#
#  Value           Meaning  Description
#      1               ISO  Identifies the root authority.
#      3              IANA  Group designation assigned by ISO.
#  *******.2312    Red Hat  Organization assigned by IANA.
#     15            Cygwin  Assigned by Organization.
#      Y       Object Type  Number defining the different object type
#                             (category) such as classSchema or
#                             attributeSchema. For example, 5 defines
#                             object class.
#      Z            Object  Number identifying a particular object
#                             within the category. For example, the user
#                             class has the number 9 assigned to it.
# 
# ----------------------------------------------------------------------

# Attribute definition for cygwinHome

dn: CN=cygwin-Home,CN=Schema,CN=Configuration,DC=X
changetype: ntdsschemaadd
objectClass: top
objectClass: attributeSchema
cn: cygwin-Home
attributeID: *******.4.1.2312.15.2.1
# attributeSyntax ******** is String(Unicode) with oMSyntax == 64
# see http://technet.microsoft.com/en-us/library/cc961740.aspx
attributeSyntax: ********
isSingleValued: TRUE
adminDisplayName: cygwin-Home
adminDescription: cygwin-Home
oMSyntax: 64
searchFlags: 1
lDAPDisplayName: cygwinHome
systemOnly: FALSE

dn:
changetype: modify
add: schemaUpdateNow
schemaUpdateNow: 1
-

# Attribute definition for cygwinShell

dn: CN=cygwin-Shell,CN=Schema,CN=Configuration,DC=X
changetype: ntdsschemaadd
objectClass: top
objectClass: attributeSchema
cn: cygwin-Shell
attributeID: *******.4.1.2312.15.2.2
attributeSyntax: ********
isSingleValued: TRUE
adminDisplayName: cygwin-Shell
adminDescription: cygwin-Shell
oMSyntax: 64
searchFlags: 1
lDAPDisplayName: cygwinShell
systemOnly: FALSE

dn:
changetype: modify
add: schemaUpdateNow
schemaUpdateNow: 1
-

# Attribute definition for cygwinGecos

dn: CN=cygwin-Gecos,CN=Schema,CN=Configuration,DC=X
changetype: ntdsschemaadd
objectClass: top
objectClass: attributeSchema
cn: cygwin-Gecos
attributeID: *******.4.1.2312.15.2.3
attributeSyntax: ********
isSingleValued: TRUE
adminDisplayName: cygwin-Gecos
adminDescription: cygwin-Gecos
oMSyntax: 64
searchFlags: 1
lDAPDisplayName: cygwinGecos
systemOnly: FALSE

dn:
changetype: modify
add: schemaUpdateNow
schemaUpdateNow: 1
-

# Attribute definition for cygwinFstab

dn: CN=cygwin-Fstab,CN=Schema,CN=Configuration,DC=X
changetype: ntdsschemaadd
objectClass: top
objectClass: attributeSchema
cn: cygwin-Fstab
attributeID: *******.4.1.2312.15.2.4
attributeSyntax: ********
isSingleValued: FALSE
adminDisplayName: cygwin-Fstab
adminDescription: cygwin-Fstab
oMSyntax: 64
searchFlags: 1
lDAPDisplayName: cygwinFstab
systemOnly: FALSE

dn:
changetype: modify
add: schemaUpdateNow
schemaUpdateNow: 1
-

# Attribute definition for cygwinUnixUid

dn: CN=cygwin-UnixUid,CN=Schema,CN=Configuration,DC=X
changetype: ntdsschemaadd
objectClass: top
objectClass: attributeSchema
cn: cygwin-UnixUid
attributeID: *******.4.1.2312.15.2.5
# attributeSyntax ******* is Integer with oMSyntax == 2
attributeSyntax: *******
isSingleValued: TRUE
adminDisplayName: cygwin-UnixUid
adminDescription: cygwin-UnixUid
oMSyntax: 2
searchFlags: 1
lDAPDisplayName: cygwinUnixUid
systemOnly: FALSE

dn:
changetype: modify
add: schemaUpdateNow
schemaUpdateNow: 1
-

# Attribute definition for cygwinUnixGid

dn: CN=cygwin-UnixGid,CN=Schema,CN=Configuration,DC=X
changetype: ntdsschemaadd
objectClass: top
objectClass: attributeSchema
cn: cygwin-UnixGid
attributeID: *******.4.1.2312.15.2.6
attributeSyntax: *******
isSingleValued: TRUE
adminDisplayName: cygwin-UnixGid
adminDescription: cygwin-UnixGid
oMSyntax: 2
searchFlags: 1
lDAPDisplayName: cygwinUnixGid
systemOnly: FALSE

dn:
changetype: modify
add: schemaUpdateNow
schemaUpdateNow: 1
-

# cygwin-User auxiliary class

dn: CN=cygwin-User,CN=Schema,CN=Configuration,DC=X
changetype: ntdsschemaadd
objectClass: top
objectClass: classSchema
cn: cygwin-User
governsID: *******.4.1.2312.15.1.1
mayContain: cygwinHome
mayContain: cygwinShell
mayContain: cygwinGecos
mayContain: cygwinFstab
mayContain: cygwinUnixUid
rDNAttID: cn
adminDisplayName: cygwin-User
adminDescription: cygwin-User
objectClassCategory: 3
lDAPDisplayName: cygwinUser
name: cygwin-User
systemOnly: FALSE

dn:
changetype: modify
add: schemaUpdateNow
schemaUpdateNow: 1
-

# cygwin-Group auxiliary class

dn: CN=cygwin-Group,CN=Schema,CN=Configuration,DC=X
changetype: ntdsschemaadd
objectClass: top
objectClass: classSchema
cn: cygwin-Group
governsID: *******.4.1.2312.15.1.2
mayContain: cygwinUnixGid
rDNAttID: cn
adminDisplayName: cygwin-Group
adminDescription: cygwin-Group
objectClassCategory: 3
lDAPDisplayName: cygwinGroup
name: cygwin-Group
systemOnly: FALSE

dn:
changetype: modify
add: schemaUpdateNow
schemaUpdateNow: 1
-

# Attach cygwin-User to User

dn: CN=User,CN=Schema,CN=Configuration,DC=X
changetype: ntdsschemamodify
add: auxiliaryClass
auxiliaryClass: cygwinUser
-

dn:
changetype: modify
add: schemaUpdateNow
schemaUpdateNow: 1
-

# Attach cygwin-Group to Group

dn: CN=Group,CN=Schema,CN=Configuration,DC=X
changetype: ntdsschemamodify
add: auxiliaryClass
auxiliaryClass: cygwinGroup
-

dn:
changetype: modify
add: schemaUpdateNow
schemaUpdateNow: 1
-
