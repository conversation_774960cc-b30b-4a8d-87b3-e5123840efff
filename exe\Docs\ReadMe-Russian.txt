﻿WINMERGE

WinMerge это инструмент сравнения и слияния файлов для Windows с открытым исходным кодом. WinMerge может
сравнивать как папки, так и файлы, представляя различия в визуальном текстовом формате, 
который легко понять и обработать. WinMerge можно использовать как внешний
инструмент для разделения / слияния или как отдельное приложение.

WinMerge имеет множество полезных вспомогательных функций, которые делают сравнение, синхронизацию
и объединение максимально простыми и полезными. Несколько языков программирования и
другие форматы файлов выделены синтаксисом.

Последняя версия WinMerge и информация о WinMerge доступны по адресу
https://winmerge.org /.

Быстрый старт
===========
Чтобы узнать, как выполнять основные операции после установки WinMerge, нажмите
Помощь>Справка WinMerge и перейдите к разделу быстрого старта. Или перейдите к веб
-версии по адресу https://manual.winmerge.org/Quick_start.html .

Помощь WinMerge
=============
Справка WinMerge устанавливается локально в виде файла справки Microsoft HTML WinMerge.chm
при установке WinMerge. Чтобы открыть справку, выберите Справка>Справка WinMerge или нажмите клавишу F1 в
окне WinMerge. В командной строке запустите исполняемый файл WinMerge с
помощью переключателя /? help.

Вы также можете просмотреть HTML-версию справки WinMerge по адресу
https://manual.winmerge.org /.

Поддержка WinMerge
================
Есть вопросы или предложения по поводу WinMerge? Хорошим местом для начала является
доска объявлений сообщества WinMerge по адресу https://forums.winmerge.org /. Разработчики часто
читают вопросы на обоих форумах и отвечают на них. Используйте Открытый дискуссионный форум для
решения общих вопросов WinMerge, таких как вопросы об использовании.
По вопросам разработки WinMerge обращайтесь на форум разработчиков.

Ошибки и запросы на новые функции
=========================
Если проблема не решена на форумах WinMerge, проверьте
трекеры проектов: перейдите на https://project.winmerge.org /, и нажмите на ссылку в
меню трекеров, например, "Ошибки" и "Запросы функций", где вы можете просматривать или отправлять элементы.

Если вы сообщаете об ошибке, пожалуйста, укажите номер версии WinMerge в своем
отчете. Вы можете сгенерировать журнал конфигурации, нажав Помощь>Конфигурация.
Пожалуйста, приложите журнал конфигурации к сообщению об ошибке; в нем содержится много полезной
информации для разработчиков.


- Разработчики WinMerge
