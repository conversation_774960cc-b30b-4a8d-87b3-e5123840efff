﻿WINMERGE

WinMerge is an Open Source comparing and merging tool for Windows. WinMerge can
compare both folders and files, presenting differences in a visual text format
that is easy to understand and handle. WinMerge can be used as an external
differencing/merging tool or as a standalone application.

WinMerge has many helpful supporting features to make comparing, synchronising,
and merging as easy and useful as possible. Several programming languages and
other file formats are syntax-highlighted.

The latest WinMerge version and WinMerge information is available at
https://winmerge.org/.

Quick start
===========
To learn how to perform basic operations after installing WinMerge, click
Help>WinMerge Help and navigate to the Quick start topic.  Or, go to the Web
version at https://manual.winmerge.org/Quick_start.html.

WinMerge Help
============= 
WinMerge Help is installed locally as a Microsoft HTML Help file, WinMerge.chm,
when you install WinMerge. To open Help, click Help>WinMerge Help or press F1 in
the WinMerge window. On the command line, run the WinMerge executable with the 
/? help switch.

You can also browse the HTML version of WinMerge Help at 
https://manual.winmerge.org/.

WinMerge Support
================
Questions or suggestions about WinMerge? A good place to start is the WinMerge
community bulletin board at https://forums.winmerge.org/. Developers frequently
read and respond to questions in both forums. Use the Open Discussion forum for
general WinMerge issues, such as questions about usage. Use the Developers forum
for WinMerge development issues.

Bugs and feature requests
=========================
If an issue is not resolved in the the WinMerge forums, check the project
trackers: go to https://project.winmerge.org/, and click a link in the Trackers
menu, like Bugs and Feature Requests, where you can browse or submit items.

If you submit a bug, please include the WinMerge version number in your
report. You can generate a configuration log by clicking Help>Configuration.
Please attach the configuration log to the bug report; it has lots of useful
information for developers.


- The WinMerge Developers
