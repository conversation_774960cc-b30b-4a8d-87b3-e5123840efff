# This file is part from WinMerge <https://winmerge.org/>
# Released under the "GNU General Public License"
#
# Translators:
# * <PERSON><PERSON><PERSON><PERSON><PERSON> <a.rostamzadeh at gmail.com>
#
msgid ""
msgstr ""
"Project-Id-Version: WinMerge Shell Extension\n"
"Report-Msgid-Bugs-To: https://bugs.winmerge.org/\n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: Persian <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-Basepath: ../../ShellExtension/Languages\n"
"Language: fa\n"
"X-Generator: Poedit 2.1.1\n"

#. AFX_TARG_*
#: ShellExtension.rc:19
#, c-format
msgid "AFX_TARG_ENU"
msgstr "AFX_TARG_FAR"

#. LANGUAGE, SUBLANGUAGE
#: ShellExtension.rc:20
#, c-format
msgid "LANG_ENGLISH, SUBLANG_ENGLISH_US"
msgstr "LANG_FARSI, SUBLANG_NEUTRAL"

#: ShellExtension.rc:111
#, c-format
msgid "ShellExtension"
msgstr " شل اکستنشن "

#: ShellExtension.rc:112
#, c-format
msgid "Win&Merge"
msgstr ""

#: ShellExtension.rc:113
#, c-format
msgid "Compare &As"
msgstr ""

#: ShellExtension.rc:114
#, c-format
msgid "&Compare"
msgstr ""

#: ShellExtension.rc:115
#, c-format
msgid "Compare&..."
msgstr ""

#: ShellExtension.rc:116
#, c-format
msgid "Select &Left"
msgstr ""

#: ShellExtension.rc:117
#, c-format
msgid "Select &Middle"
msgstr ""

#: ShellExtension.rc:118
#, c-format
msgid "Re-select &Left"
msgstr ""
